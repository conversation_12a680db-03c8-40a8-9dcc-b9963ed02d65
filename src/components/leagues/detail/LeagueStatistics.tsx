'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useLeagueStatistics } from '@/lib/hooks/leagues/useLeagueStatistics';
import { League } from '@/lib/types/api';
import {
      TrendingUp,
      Users,
      Calendar,
      Activity,
      Trophy,
      Target,
      BarChart3,
      Timer
} from 'lucide-react';

interface LeagueStatisticsProps {
      league: League;
      season?: number;
      className?: string;
}

export function LeagueStatistics({ league, season, className = '' }: LeagueStatisticsProps) {
      const { data: statistics, isLoading, error } = useLeagueStatistics(league.externalId, season);

      if (isLoading) {
            return (
                  <Card className={className}>
                        <CardHeader>
                              <CardTitle className="flex items-center space-x-2">
                                    <BarChart3 className="w-5 h-5" />
                                    <span>League Statistics</span>
                              </CardTitle>
                        </CardHeader>
                        <CardContent>
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                    {Array.from({ length: 8 }).map((_, i) => (
                                          <div key={i} className="space-y-2">
                                                <Skeleton className="h-4 w-20" />
                                                <Skeleton className="h-8 w-16" />
                                          </div>
                                    ))}
                              </div>
                        </CardContent>
                  </Card>
            );
      }

      if (error || !statistics) {
            return (
                  <Card className={className}>
                        <CardHeader>
                              <CardTitle className="flex items-center space-x-2">
                                    <BarChart3 className="w-5 h-5" />
                                    <span>League Statistics</span>
                              </CardTitle>
                        </CardHeader>
                        <CardContent>
                              <div className="text-center py-8 text-gray-500">
                                    <Activity className="w-8 h-8 mx-auto mb-2 opacity-50" />
                                    <p className="text-sm">Statistics not available</p>
                              </div>
                        </CardContent>
                  </Card>
            );
      }

      const stats = [
            {
                  label: 'Total Teams',
                  value: statistics.totalTeams || 0,
                  icon: Users,
                  color: 'text-blue-600',
                  bgColor: 'bg-blue-50',
            },
            {
                  label: 'Total Fixtures',
                  value: statistics.totalFixtures || 0,
                  icon: Calendar,
                  color: 'text-green-600',
                  bgColor: 'bg-green-50',
            },
            {
                  label: 'Completed',
                  value: statistics.completedFixtures || 0,
                  icon: Trophy,
                  color: 'text-yellow-600',
                  bgColor: 'bg-yellow-50',
            },
            {
                  label: 'Upcoming',
                  value: statistics.upcomingFixtures || 0,
                  icon: Timer,
                  color: 'text-purple-600',
                  bgColor: 'bg-purple-50',
            },
            {
                  label: 'Live Matches',
                  value: statistics.liveFixtures || 0,
                  icon: Activity,
                  color: 'text-red-600',
                  bgColor: 'bg-red-50',
            },
            {
                  label: 'Season Progress',
                  value: `${statistics.seasonProgress || 0}%`,
                  icon: Target,
                  color: 'text-indigo-600',
                  bgColor: 'bg-indigo-50',
            },
            {
                  label: 'Current Round',
                  value: statistics.currentRound || 'N/A',
                  icon: TrendingUp,
                  color: 'text-orange-600',
                  bgColor: 'bg-orange-50',
            },
            {
                  label: 'Total Goals',
                  value: statistics.totalGoals || 0,
                  icon: Target,
                  color: 'text-teal-600',
                  bgColor: 'bg-teal-50',
            },
      ];

      return (
            <Card className={className}>
                  <CardHeader>
                        <CardTitle className="flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                    <BarChart3 className="w-5 h-5" />
                                    <span>League Statistics</span>
                              </div>
                              <Badge variant="outline" className="text-xs">
                                    Season {league.season}
                              </Badge>
                        </CardTitle>
                  </CardHeader>
                  <CardContent>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                              {stats.map((stat, index) => {
                                    const Icon = stat.icon;
                                    return (
                                          <div
                                                key={index}
                                                className="flex items-center space-x-3 p-3 rounded-lg border border-gray-100 hover:border-gray-200 transition-colors"
                                          >
                                                <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                                                      <Icon className={`w-4 h-4 ${stat.color}`} />
                                                </div>
                                                <div>
                                                      <p className="text-xs text-gray-500 font-medium">{stat.label}</p>
                                                      <p className="text-lg font-bold text-gray-900">{stat.value}</p>
                                                </div>
                                          </div>
                                    );
                              })}
                        </div>

                        {/* Top Performers */}
                        {(statistics.topScorer?.team || statistics.bestDefense?.team) && (
                              <div className="mt-6 pt-6 border-t border-gray-100">
                                    <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                                          <Trophy className="w-4 h-4 mr-2" />
                                          Top Performers
                                    </h4>
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                          {statistics.topScorer?.team && (
                                                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                                                      <div>
                                                            <p className="text-xs text-green-600 font-medium">Top Scorer</p>
                                                            <p className="text-sm font-bold text-green-800">{statistics.topScorer.team}</p>
                                                      </div>
                                                      <div className="text-right">
                                                            <p className="text-lg font-bold text-green-800">{statistics.topScorer.goals}</p>
                                                            <p className="text-xs text-green-600">goals</p>
                                                      </div>
                                                </div>
                                          )}
                                          {statistics.bestDefense?.team && (
                                                <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                                                      <div>
                                                            <p className="text-xs text-blue-600 font-medium">Best Defense</p>
                                                            <p className="text-sm font-bold text-blue-800">{statistics.bestDefense.team}</p>
                                                      </div>
                                                      <div className="text-right">
                                                            <p className="text-lg font-bold text-blue-800">{statistics.bestDefense.goalsAgainst}</p>
                                                            <p className="text-xs text-blue-600">conceded</p>
                                                      </div>
                                                </div>
                                          )}
                                    </div>
                              </div>
                        )}

                        {/* Additional Insights */}
                        {statistics.insights && statistics.insights.length > 0 && (
                              <div className="mt-6 pt-6 border-t border-gray-100">
                                    <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                                          <TrendingUp className="w-4 h-4 mr-2" />
                                          Key Insights
                                    </h4>
                                    <div className="space-y-2">
                                          {statistics.insights.map((insight, index) => (
                                                <div key={index} className="flex items-start space-x-2">
                                                      <div className="w-1.5 h-1.5 rounded-full bg-blue-500 mt-2 flex-shrink-0"></div>
                                                      <p className="text-sm text-gray-600">{insight}</p>
                                                </div>
                                          ))}
                                    </div>
                              </div>
                        )}

                        {/* Coverage Breakdown */}
                        {league.season_detail?.coverage && (
                              <div className="mt-6 pt-6 border-t border-gray-100">
                                    <h4 className="text-sm font-medium text-gray-700 mb-3">Coverage Breakdown</h4>
                                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                                          {Object.entries(league.season_detail.coverage).map(([key, value]) => {
                                                if (key === 'fixtures') return null; // Skip nested object
                                                return (
                                                      <div key={key} className="flex items-center justify-between">
                                                            <span className="text-xs text-gray-500 capitalize">
                                                                  {key.replace('_', ' ')}
                                                            </span>
                                                            <div className={`w-2 h-2 rounded-full ${value ? 'bg-green-500' : 'bg-red-500'}`}></div>
                                                      </div>
                                                );
                                          })}
                                    </div>
                              </div>
                        )}
                  </CardContent>
            </Card>
      );
}

export default LeagueStatistics;
