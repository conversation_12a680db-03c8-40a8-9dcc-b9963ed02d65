"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/leagues/[id]/page",{

/***/ "(app-pages-browser)/./src/components/leagues/detail/TeamQuickViewModal.tsx":
/*!**************************************************************!*\
  !*** ./src/components/leagues/detail/TeamQuickViewModal.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TeamQuickViewModal: function() { return /* binding */ TeamQuickViewModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3Icon,CalendarIcon,ExternalLinkIcon,MapPinIcon,TrophyIcon,UsersIcon,XIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3Icon,CalendarIcon,ExternalLinkIcon,MapPinIcon,TrophyIcon,UsersIcon,XIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3Icon,CalendarIcon,ExternalLinkIcon,MapPinIcon,TrophyIcon,UsersIcon,XIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3Icon,CalendarIcon,ExternalLinkIcon,MapPinIcon,TrophyIcon,UsersIcon,XIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3Icon,CalendarIcon,ExternalLinkIcon,MapPinIcon,TrophyIcon,UsersIcon,XIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3Icon,CalendarIcon,ExternalLinkIcon,MapPinIcon,TrophyIcon,UsersIcon,XIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3Icon,CalendarIcon,ExternalLinkIcon,MapPinIcon,TrophyIcon,UsersIcon,XIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* __next_internal_client_entry_do_not_use__ TeamQuickViewModal,default auto */ \n\n\n\n\n\n\n\nconst TeamQuickViewModal = (param)=>{\n    let { team, isOpen, onClose, onViewDetails } = param;\n    if (!team) return null;\n    const winRate = team.stats.matchesPlayed > 0 ? Math.round(team.stats.wins / team.stats.matchesPlayed * 100) : 0;\n    const goalDifference = team.stats.goalsFor - team.stats.goalsAgainst;\n    const getFormColor = (result)=>{\n        switch(result){\n            case \"W\":\n                return \"bg-green-500\";\n            case \"L\":\n                return \"bg-red-500\";\n            case \"D\":\n                return \"bg-yellow-500\";\n            default:\n                return \"bg-gray-500\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: isOpen,\n        onOpenChange: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"max-w-2xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                                    className: \"text-xl font-semibold\",\n                                    children: \"Team Quick View\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    onClick: onClose,\n                                    className: \"h-8 w-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 43\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 31\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                                    className: \"h-16 w-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarImage, {\n                                            src: team.logo,\n                                            alt: \"\".concat(team.name, \" logo\")\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                                            className: \"bg-blue-500 text-white text-lg font-bold\",\n                                            children: team.name.substring(0, 2).toUpperCase()\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 43\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: team.name\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 text-sm text-muted-foreground mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                            lineNumber: 91,\n                                                            columnNumber: 55\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"Founded \",\n                                                                team.foundedYear\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                            lineNumber: 92,\n                                                            columnNumber: 55\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                            lineNumber: 95,\n                                                            columnNumber: 55\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                team.city,\n                                                                \", \",\n                                                                team.country\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                            lineNumber: 96,\n                                                            columnNumber: 55\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 43\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 37\n                                }, undefined),\n                                team.stats.position && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                    variant: \"secondary\",\n                                    className: \"text-lg px-3 py-1\",\n                                    children: [\n                                        \"#\",\n                                        team.stats.position\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 43\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 31\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 25\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        team.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-muted-foreground leading-relaxed\",\n                                children: team.description\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 43\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 37\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_5__.Separator, {}, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 31\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-3 bg-muted/50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: team.stats.points\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Points\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 43\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-3 bg-muted/50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: [\n                                                winRate,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Win Rate\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 43\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-3 bg-muted/50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold \".concat(goalDifference >= 0 ? \"text-green-600\" : \"text-red-600\"),\n                                            children: [\n                                                goalDifference >= 0 ? \"+\" : \"\",\n                                                goalDifference\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Goal Diff\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 43\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-3 bg-muted/50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: team.stats.matchesPlayed\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Matches\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 43\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 31\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold mb-3 flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Recent Form (Last 5 matches)\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 43\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        team.recentForm.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold \".concat(getFormColor(result)),\n                                                children: result\n                                            }, index, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 49\n                                            }, undefined)),\n                                        team.recentForm.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"No recent matches\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 49\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 31\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold mb-3\",\n                                    children: \"Season Statistics\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-4 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"Wins:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-green-600\",\n                                                    children: team.stats.wins\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"Draws:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-yellow-600\",\n                                                    children: team.stats.draws\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"Losses:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-red-600\",\n                                                    children: team.stats.losses\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"Goals For:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: team.stats.goalsFor\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"Goals Against:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: team.stats.goalsAgainst\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"Total Points:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-blue-600\",\n                                                    children: team.stats.points\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 43\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 31\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_5__.Separator, {}, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 31\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                            children: [\n                                team.stadium && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-muted-foreground\",\n                                            children: \"Stadium:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: team.stadium\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        team.capacity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-muted-foreground\",\n                                            children: [\n                                                \"(\",\n                                                team.capacity.toLocaleString(),\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 55\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 43\n                                }, undefined),\n                                team.manager && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-muted-foreground\",\n                                            children: \"Manager:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: team.manager\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 49\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 215,\n                                    columnNumber: 43\n                                }, undefined),\n                                team.playersCount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-muted-foreground\",\n                                            children: \"Squad Size:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: [\n                                                team.playersCount,\n                                                \" players\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 49\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 43\n                                }, undefined),\n                                team.website && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: team.website,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"text-blue-600 hover:underline font-medium\",\n                                            children: \"Official Website\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 49\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 43\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 31\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 25\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogFooter, {\n                    className: \"space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            onClick: onClose,\n                            children: \"Close\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 245,\n                            columnNumber: 31\n                        }, undefined),\n                        onViewDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: ()=>onViewDetails(team.id),\n                            children: \"View Full Details\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 37\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 25\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n            lineNumber: 63,\n            columnNumber: 19\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n        lineNumber: 62,\n        columnNumber: 13\n    }, undefined);\n};\n_c = TeamQuickViewModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TeamQuickViewModal);\nvar _c;\n$RefreshReg$(_c, \"TeamQuickViewModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/leagues/detail/TeamQuickViewModal.tsx\n"));

/***/ })

});