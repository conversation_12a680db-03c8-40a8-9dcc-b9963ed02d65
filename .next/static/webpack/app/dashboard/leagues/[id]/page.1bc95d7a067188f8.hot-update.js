"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/leagues/[id]/page",{

/***/ "(app-pages-browser)/./src/lib/hooks/leagues/useLeagueStandings.ts":
/*!*****************************************************!*\
  !*** ./src/lib/hooks/leagues/useLeagueStandings.ts ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLeagueStandings: function() { return /* binding */ useLeagueStandings; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useLeagueStandings auto */ \n// Fetch standings from API\nconst fetchStandingsFromAPI = async (leagueId, season, format)=>{\n    try {\n        var _apiData_data, _apiData_data1;\n        const currentSeason = season || new Date().getFullYear();\n        const formatParam = format ? \"&format=\".concat(format) : \"\";\n        console.log(\"\\uD83D\\uDD04 Fetching standings for league:\", leagueId, \"season:\", currentSeason);\n        // Use proxy endpoint through Next.js frontend with cache busting\n        const timestamp = Date.now();\n        const response = await fetch(\"/api/standings?league=\".concat(leagueId, \"&season=\").concat(currentSeason).concat(formatParam, \"&_t=\").concat(timestamp));\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch standings: \".concat(response.status));\n        }\n        const apiData = await response.json();\n        console.log(\"✅ Standings API response:\", apiData);\n        console.log(\"\\uD83D\\uDD0D API data structure check:\", {\n            hasData: !!apiData.data,\n            isArray: Array.isArray(apiData.data),\n            dataLength: (_apiData_data = apiData.data) === null || _apiData_data === void 0 ? void 0 : _apiData_data.length,\n            firstItem: (_apiData_data1 = apiData.data) === null || _apiData_data1 === void 0 ? void 0 : _apiData_data1[0]\n        });\n        if (!apiData.data || !Array.isArray(apiData.data)) {\n            console.warn(\"⚠️ No standings data in API response, using fallback\");\n            return generateFallbackStandings(leagueId);\n        }\n        // Transform API data to our interface\n        console.log(\"\\uD83D\\uDD04 Transforming API data to interface, count:\", apiData.data.length);\n        const transformedData = apiData.data.map((standing, index)=>{\n            var _standing_teamId, _standing_team_externalId, _standing_team, _standing_team1, _standing_team2, _standing_goals, _standing_goals1, _standing_team3, _standing_team4, _standing_team5;\n            // Parse form data from API (e.g., \"WWWWW\" -> ['W', 'W', 'W', 'W', 'W'])\n            let form = [];\n            if (standing.form && typeof standing.form === \"string\") {\n                form = standing.form.split(\"\").slice(0, 5);\n            } else {\n                // Fallback to random form if not provided\n                const formResults = [\n                    \"W\",\n                    \"L\",\n                    \"D\"\n                ];\n                form = Array.from({\n                    length: 5\n                }, ()=>formResults[Math.floor(Math.random() * formResults.length)]);\n            }\n            return {\n                position: standing.position || standing.rank || index + 1,\n                team: {\n                    id: ((_standing_teamId = standing.teamId) === null || _standing_teamId === void 0 ? void 0 : _standing_teamId.toString()) || ((_standing_team = standing.team) === null || _standing_team === void 0 ? void 0 : (_standing_team_externalId = _standing_team.externalId) === null || _standing_team_externalId === void 0 ? void 0 : _standing_team_externalId.toString()) || (index + 1).toString(),\n                    name: standing.teamName || ((_standing_team1 = standing.team) === null || _standing_team1 === void 0 ? void 0 : _standing_team1.name) || \"Team \".concat(index + 1),\n                    logo: standing.teamLogo || ((_standing_team2 = standing.team) === null || _standing_team2 === void 0 ? void 0 : _standing_team2.logo) || \"\"\n                },\n                points: standing.points || 0,\n                playedGames: standing.played || standing.playedGames || 0,\n                wins: standing.win || standing.wins || 0,\n                draws: standing.draw || standing.draws || 0,\n                losses: standing.lose || standing.losses || 0,\n                goalsFor: standing.goalsFor || ((_standing_goals = standing.goals) === null || _standing_goals === void 0 ? void 0 : _standing_goals.for) || 0,\n                goalsAgainst: standing.goalsAgainst || ((_standing_goals1 = standing.goals) === null || _standing_goals1 === void 0 ? void 0 : _standing_goals1.against) || 0,\n                goalDifference: standing.goalsDiff !== undefined ? standing.goalsDiff : standing.goalDifference !== undefined ? standing.goalDifference : (standing.goalsFor || 0) - (standing.goalsAgainst || 0),\n                form,\n                // Store original API fields\n                externalId: standing.externalId || standing.id,\n                teamId: standing.teamId || ((_standing_team3 = standing.team) === null || _standing_team3 === void 0 ? void 0 : _standing_team3.externalId),\n                teamName: standing.teamName || ((_standing_team4 = standing.team) === null || _standing_team4 === void 0 ? void 0 : _standing_team4.name),\n                teamLogo: standing.teamLogo || ((_standing_team5 = standing.team) === null || _standing_team5 === void 0 ? void 0 : _standing_team5.logo)\n            };\n        });\n        const sortedData = transformedData.sort((a, b)=>a.position - b.position);\n        console.log(\"✅ Transformed standings data:\", sortedData.slice(0, 3).map((s)=>({\n                position: s.position,\n                teamName: s.team.name,\n                points: s.points\n            })));\n        return sortedData;\n    } catch (error) {\n        console.error(\"❌ Error fetching standings:\", error);\n        throw error;\n    }\n};\n// Generate fallback standings data for development/testing\nconst generateFallbackStandings = (leagueId)=>{\n    const teams = [\n        {\n            id: \"33\",\n            name: \"Manchester United\",\n            logo: \"https://media.api-sports.io/football/teams/33.png\"\n        },\n        {\n            id: \"50\",\n            name: \"Manchester City\",\n            logo: \"https://media.api-sports.io/football/teams/50.png\"\n        },\n        {\n            id: \"42\",\n            name: \"Arsenal\",\n            logo: \"https://media.api-sports.io/football/teams/42.png\"\n        },\n        {\n            id: \"40\",\n            name: \"Liverpool\",\n            logo: \"https://media.api-sports.io/football/teams/40.png\"\n        },\n        {\n            id: \"49\",\n            name: \"Chelsea\",\n            logo: \"https://media.api-sports.io/football/teams/49.png\"\n        },\n        {\n            id: \"47\",\n            name: \"Tottenham\",\n            logo: \"https://media.api-sports.io/football/teams/47.png\"\n        },\n        {\n            id: \"34\",\n            name: \"Newcastle\",\n            logo: \"https://media.api-sports.io/football/teams/34.png\"\n        },\n        {\n            id: \"66\",\n            name: \"Aston Villa\",\n            logo: \"https://media.api-sports.io/football/teams/66.png\"\n        },\n        {\n            id: \"51\",\n            name: \"Brighton\",\n            logo: \"https://media.api-sports.io/football/teams/51.png\"\n        },\n        {\n            id: \"39\",\n            name: \"Wolves\",\n            logo: \"https://media.api-sports.io/football/teams/39.png\"\n        }\n    ];\n    return teams.map((team, index)=>{\n        const played = 20 + Math.floor(Math.random() * 10);\n        const wins = Math.floor(Math.random() * played * 0.7);\n        const losses = Math.floor(Math.random() * (played - wins) * 0.6);\n        const draws = played - wins - losses;\n        const goalsFor = wins * 2 + draws + Math.floor(Math.random() * 10);\n        const goalsAgainst = losses * 2 + Math.floor(Math.random() * goalsFor * 0.8);\n        // Generate realistic form (last 5 matches)\n        const formResults = [\n            \"W\",\n            \"L\",\n            \"D\"\n        ];\n        const form = Array.from({\n            length: 5\n        }, ()=>formResults[Math.floor(Math.random() * formResults.length)]);\n        return {\n            position: index + 1,\n            team,\n            points: wins * 3 + draws,\n            playedGames: played,\n            wins,\n            draws,\n            losses,\n            goalsFor,\n            goalsAgainst,\n            goalDifference: goalsFor - goalsAgainst,\n            form,\n            externalId: parseInt(team.id),\n            teamId: parseInt(team.id),\n            teamName: team.name,\n            teamLogo: team.logo\n        };\n    }).sort((a, b)=>b.points - a.points || b.goalDifference - a.goalDifference);\n};\nconst useLeagueStandings = (param)=>{\n    let { leagueId, season, format = \"external\" } = param;\n    const [standings, setStandings] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const refreshStandings = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const standingsData = await fetchStandingsFromAPI(leagueId, season, format);\n            setStandings(standingsData);\n        } catch (err) {\n            console.error(\"❌ Error fetching standings:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to fetch standings\");\n            // Use fallback data in case of error\n            console.warn(\"⚠️ Using fallback standings data due to error\");\n            setStandings(generateFallbackStandings(leagueId));\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (leagueId) {\n            // Clear any existing data first\n            setStandings([]);\n            setError(null);\n            refreshStandings();\n        }\n    }, [\n        leagueId,\n        season,\n        format\n    ]);\n    // Calculate statistics\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const totalTeams = standings.length;\n        const topTeam = standings.length > 0 ? standings[0] : null;\n        const totalGames = standings.reduce((sum, team)=>sum + team.playedGames, 0);\n        const totalGoals = standings.reduce((sum, team)=>sum + team.goalsFor, 0);\n        const avgGoalsPerGame = totalGames > 0 ? Math.round(totalGoals / totalGames * 100) / 100 : 0;\n        return {\n            totalTeams,\n            topTeam,\n            avgGoalsPerGame\n        };\n    }, [\n        standings\n    ]);\n    return {\n        standings,\n        loading,\n        error,\n        refreshStandings,\n        stats\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/hooks/leagues/useLeagueStandings.ts\n"));

/***/ })

});