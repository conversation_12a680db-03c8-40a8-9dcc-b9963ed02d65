import { NextRequest, NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';

export async function GET(request: NextRequest) {
      try {
            const { searchParams } = new URL(request.url);
            const league = searchParams.get('league');
            const season = searchParams.get('season');
            const format = searchParams.get('format');
            // Remove timestamp parameter as it's only for cache busting
            searchParams.delete('_t');

            // Validate required parameters
            if (!league) {
                  return NextResponse.json(
                        { error: 'League parameter is required' },
                        { status: 400 }
                  );
            }

            // Build API URL with parameters
            const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';
            let apiUrl = `${apiBaseUrl}/football/standings?league=${league}`;

            if (season) {
                  apiUrl += `&season=${season}`;
            }

            if (format) {
                  apiUrl += `&format=${format}`;
            }

            console.log('🔄 Proxying standings request to:', apiUrl);

            // Make request to backend API
            const response = await fetch(apiUrl, {
                  method: 'GET',
                  headers: {
                        'Content-Type': 'application/json',
                  },
            });

            if (!response.ok) {
                  console.error('❌ Backend API error:', response.status, response.statusText);
                  return NextResponse.json(
                        { error: `Backend API error: ${response.status}` },
                        { status: response.status }
                  );
            }

            const data = await response.json();
            console.log('✅ Standings API response received');

            // Return with no-cache headers to prevent caching issues
            const jsonResponse = NextResponse.json(data);
            jsonResponse.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
            jsonResponse.headers.set('Pragma', 'no-cache');
            jsonResponse.headers.set('Expires', '0');
            return jsonResponse;

      } catch (error) {
            console.error('❌ Standings API proxy error:', error);
            return NextResponse.json(
                  { error: 'Failed to fetch standings' },
                  { status: 500 }
            );
      }
}
