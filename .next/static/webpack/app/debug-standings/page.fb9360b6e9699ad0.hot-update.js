"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/debug-standings/page",{

/***/ "(app-pages-browser)/./src/app/debug-standings/page.tsx":
/*!******************************************!*\
  !*** ./src/app/debug-standings/page.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DebugStandingsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DebugStandingsPage() {\n    var _apiResponse_response, _apiResponse_response__league_standings_, _apiResponse_response__league_standings, _apiResponse_response__league, _apiResponse_response_, _apiResponse_response1, _apiResponse_response__league_standings1, _apiResponse_response__league1, _apiResponse_response_1, _apiResponse_response2;\n    _s();\n    const [apiResponse, setApiResponse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const testAPI = async ()=>{\n        setLoading(true);\n        setError(null);\n        setApiResponse(null);\n        try {\n            var _data_response__league_standings, _data_response__league, _data_response_, _data_response, _data_response1, _standingsArray__team, _standingsArray_, _standingsArray_1;\n            console.log(\"\\uD83D\\uDD04 Testing API call...\");\n            const apiUrl = \"/api/standings?league=340&season=2025&format=external&_t=\".concat(Date.now());\n            console.log(\"\\uD83C\\uDF10 API URL:\", apiUrl);\n            const response = await fetch(apiUrl, {\n                method: \"GET\",\n                headers: {\n                    \"Cache-Control\": \"no-cache, no-store, must-revalidate\",\n                    \"Pragma\": \"no-cache\",\n                    \"Expires\": \"0\"\n                }\n            });\n            console.log(\"\\uD83D\\uDCE1 Response status:\", response.status, response.statusText);\n            console.log(\"\\uD83D\\uDCE1 Response headers:\", Object.fromEntries(response.headers.entries()));\n            if (!response.ok) {\n                throw new Error(\"API Error: \".concat(response.status, \" \").concat(response.statusText));\n            }\n            const data = await response.json();\n            console.log(\"✅ API Response:\", data);\n            setApiResponse(data);\n            // Validate data structure - check both old and new format\n            const standingsArray = ((_data_response = data.response) === null || _data_response === void 0 ? void 0 : (_data_response_ = _data_response[0]) === null || _data_response_ === void 0 ? void 0 : (_data_response__league = _data_response_.league) === null || _data_response__league === void 0 ? void 0 : (_data_response__league_standings = _data_response__league.standings) === null || _data_response__league_standings === void 0 ? void 0 : _data_response__league_standings[0]) || data.data || [];\n            console.log(\"\\uD83D\\uDD0D Data validation:\", {\n                hasResponse: !!data.response,\n                hasData: !!data.data,\n                responseLength: (_data_response1 = data.response) === null || _data_response1 === void 0 ? void 0 : _data_response1.length,\n                standingsLength: standingsArray.length,\n                firstItem: standingsArray[0],\n                firstTeamName: ((_standingsArray_ = standingsArray[0]) === null || _standingsArray_ === void 0 ? void 0 : (_standingsArray__team = _standingsArray_.team) === null || _standingsArray__team === void 0 ? void 0 : _standingsArray__team.name) || ((_standingsArray_1 = standingsArray[0]) === null || _standingsArray_1 === void 0 ? void 0 : _standingsArray_1.teamName)\n            });\n        } catch (err) {\n            console.error(\"❌ API Test Error:\", err);\n            setError(err instanceof Error ? err.message : \"Unknown error\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        testAPI();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-8 space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold\",\n                children: \"Debug Standings API\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            children: \"API Test Controls\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: testAPI,\n                            disabled: loading,\n                            children: loading ? \"Testing...\" : \"Test API Call\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-red-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-red-600\",\n                            children: \"Error\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-600\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this),\n            apiResponse && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            children: \"API Response Analysis\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Has Response:\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" \",\n                                            apiResponse.response ? \"✅ Yes\" : \"❌ No\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Has Data (old):\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" \",\n                                            apiResponse.data ? \"✅ Yes\" : \"❌ No\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Response Length:\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" \",\n                                            ((_apiResponse_response = apiResponse.response) === null || _apiResponse_response === void 0 ? void 0 : _apiResponse_response.length) || 0\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Standings Length:\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" \",\n                                            ((_apiResponse_response1 = apiResponse.response) === null || _apiResponse_response1 === void 0 ? void 0 : (_apiResponse_response_ = _apiResponse_response1[0]) === null || _apiResponse_response_ === void 0 ? void 0 : (_apiResponse_response__league = _apiResponse_response_.league) === null || _apiResponse_response__league === void 0 ? void 0 : (_apiResponse_response__league_standings = _apiResponse_response__league.standings) === null || _apiResponse_response__league_standings === void 0 ? void 0 : (_apiResponse_response__league_standings_ = _apiResponse_response__league_standings[0]) === null || _apiResponse_response__league_standings_ === void 0 ? void 0 : _apiResponse_response__league_standings_.length) || 0\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"First 3 Teams:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"list-disc list-inside mt-2\",\n                                        children: (((_apiResponse_response2 = apiResponse.response) === null || _apiResponse_response2 === void 0 ? void 0 : (_apiResponse_response_1 = _apiResponse_response2[0]) === null || _apiResponse_response_1 === void 0 ? void 0 : (_apiResponse_response__league1 = _apiResponse_response_1.league) === null || _apiResponse_response__league1 === void 0 ? void 0 : (_apiResponse_response__league_standings1 = _apiResponse_response__league1.standings) === null || _apiResponse_response__league_standings1 === void 0 ? void 0 : _apiResponse_response__league_standings1[0]) || []).slice(0, 3).map((team, index)=>{\n                                            var _team_team;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"#\",\n                                                    team.rank,\n                                                    \" \",\n                                                    (_team_team = team.team) === null || _team_team === void 0 ? void 0 : _team_team.name,\n                                                    \" - \",\n                                                    team.points,\n                                                    \" pts\"\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                        className: \"cursor-pointer font-medium\",\n                                        children: \"Raw Response\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"text-xs bg-gray-100 p-4 rounded mt-2 overflow-auto\",\n                                        children: JSON.stringify(apiResponse, null, 2)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                lineNumber: 94,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            children: \"Expected vs Actual\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-green-600\",\n                                            children: \"Expected (Correct)\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"#1 Nam Dinh - 54 pts\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"#2 Ha Noi - 46 pts\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"#3 C\\xf4ng An Nh\\xe2n D\\xe2n - 42 pts\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Total: 14 teams\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-red-600\",\n                                            children: \"Fallback (Wrong)\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"#1 Manchester United\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"#2 Manchester City\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"#3 Arsenal\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Total: 10 teams\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n_s(DebugStandingsPage, \"ggDnxDEcNBnUAtqiKSUXcXzXizs=\");\n_c = DebugStandingsPage;\nvar _c;\n$RefreshReg$(_c, \"DebugStandingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/debug-standings/page.tsx\n"));

/***/ })

});