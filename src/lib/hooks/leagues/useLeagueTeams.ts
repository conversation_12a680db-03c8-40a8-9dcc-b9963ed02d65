
'use client';

import { useState, useEffect, useMemo } from 'react';

export interface TeamData {
      id: string;
      name: string;
      logo?: string;
      foundedYear: number;
      country: string;
      city: string;
      stadium?: string;
      capacity?: number;
      website?: string;
      description?: string;
      stats: {
            matchesPlayed: number;
            wins: number;
            draws: number;
            losses: number;
            goalsFor: number;
            goalsAgainst: number;
            points: number;
            position?: number;
      };
      recentForm: ('W' | 'L' | 'D')[];
      manager?: string;
      playersCount?: number;
      // API fields
      externalId?: number;
      code?: string;
      venue?: {
            id: number;
            name: string;
            address?: string;
            city?: string;
            capacity?: number;
            surface?: string;
            image?: string;
      };
      founded?: number;
}

export interface UseLeagueTeamsProps {
      leagueId: string;
      season?: number;
}

export interface UseLeagueTeamsReturn {
      teams: TeamData[];
      loading: boolean;
      error: string | null;
      searchTerm: string;
      setSearchTerm: (term: string) => void;
      sortBy: 'name' | 'foundedYear' | 'country' | 'points' | 'position';
      setSortBy: (sort: 'name' | 'foundedYear' | 'country' | 'points' | 'position') => void;
      sortOrder: 'asc' | 'desc';
      setSortOrder: (order: 'asc' | 'desc') => void;
      countryFilter: string;
      setCountryFilter: (country: string) => void;
      filteredAndSortedTeams: TeamData[];
      countries: string[];
      stats: {
            totalTeams: number;
            totalCountries: number;
            averageFoundedYear: number;
            totalMatches: number;
      };
      refreshTeams: () => Promise<void>;
}

// Fetch teams from API
const fetchTeamsFromAPI = async (leagueId: string, season?: number): Promise<TeamData[]> => {
      try {
            const currentSeason = season || new Date().getFullYear();
            console.log('🔄 Fetching teams for league:', leagueId, 'season:', currentSeason);

            // Use proxy endpoint through Next.js frontend with higher limit to get all teams
            const timestamp = Date.now();
            const response = await fetch(`/api/teams?league=${leagueId}&season=${currentSeason}&limit=50&_t=${timestamp}`);

            if (!response.ok) {
                  throw new Error(`Failed to fetch teams: ${response.status}`);
            }

            const apiData = await response.json();
            console.log('✅ Teams API response:', apiData);

            if (!apiData.data || !Array.isArray(apiData.data)) {
                  console.warn('⚠️ No teams data in API response, using fallback');
                  return generateFallbackTeams(leagueId);
            }

            // Transform API data to our interface
            return apiData.data.map((team: any) => ({
                  id: team.externalId?.toString() || team.id?.toString() || Math.random().toString(),
                  name: team.name || 'Unknown Team',
                  logo: team.logo || '',
                  foundedYear: team.founded || new Date().getFullYear(),
                  country: team.country || 'Unknown',
                  city: team.venue?.city || 'Unknown',
                  stadium: team.venue?.name || 'Unknown Stadium',
                  capacity: team.venue?.capacity || 0,
                  website: '',
                  description: '',
                  stats: {
                        matchesPlayed: 0,
                        wins: 0,
                        draws: 0,
                        losses: 0,
                        goalsFor: 0,
                        goalsAgainst: 0,
                        points: 0,
                        position: undefined,
                  },
                  recentForm: [],
                  manager: '',
                  playersCount: 0,
                  externalId: team.externalId,
                  code: team.code,
                  venue: team.venue ? {
                        id: team.venue.id,
                        name: team.venue.name,
                        address: team.venue.address,
                        city: team.venue.city,
                        capacity: team.venue.capacity,
                        surface: team.venue.surface,
                        image: team.venue.image,
                  } : undefined,
                  founded: team.founded,
            }));

      } catch (error) {
            console.error('❌ Error fetching teams:', error);
            // Return fallback data on error
            return generateFallbackTeams(leagueId);
      }
};

// Fallback teams data when API fails
const generateFallbackTeams = (leagueId: string): TeamData[] => {
      const teamNames = [
            'Arsenal FC', 'Manchester United', 'Liverpool FC', 'Chelsea FC',
            'Manchester City', 'Tottenham Hotspur', 'Newcastle United', 'Brighton & Hove',
            'Aston Villa', 'West Ham United', 'Crystal Palace', 'Fulham FC',
            'Brentford FC', 'Wolverhampton', 'Everton FC', 'Nottingham Forest',
            'Bournemouth AFC', 'Sheffield United', 'Burnley FC', 'Luton Town'
      ];

      const countries = ['England', 'Scotland', 'Wales', 'Ireland'];
      const cities = [
            'London', 'Manchester', 'Liverpool', 'Birmingham', 'Newcastle',
            'Brighton', 'Sheffield', 'Burnley', 'Luton', 'Bournemouth'
      ];

      const stadiums = [
            'Emirates Stadium', 'Old Trafford', 'Anfield', 'Stamford Bridge',
            'Etihad Stadium', 'Tottenham Hotspur Stadium', 'St. James\' Park',
            'American Express Community Stadium', 'Villa Park', 'London Stadium'
      ];

      const managers = [
            'Mikel Arteta', 'Erik ten Hag', 'Jürgen Klopp', 'Mauricio Pochettino',
            'Pep Guardiola', 'Ange Postecoglou', 'Eddie Howe', 'Roberto De Zerbi',
            'Unai Emery', 'David Moyes', 'Roy Hodgson', 'Marco Silva'
      ];

      return teamNames.map((name, index) => {
            const matchesPlayed = Math.floor(Math.random() * 38) + 10;
            const wins = Math.floor(Math.random() * matchesPlayed * 0.6);
            const losses = Math.floor(Math.random() * (matchesPlayed - wins) * 0.7);
            const draws = matchesPlayed - wins - losses;
            const points = wins * 3 + draws;
            const goalsFor = Math.floor(Math.random() * 80) + 20;
            const goalsAgainst = Math.floor(Math.random() * 60) + 15;

            // Generate recent form (last 5 matches)
            const recentForm: ('W' | 'L' | 'D')[] = [];
            for (let i = 0; i < 5; i++) {
                  const rand = Math.random();
                  if (rand < 0.4) recentForm.push('W');
                  else if (rand < 0.7) recentForm.push('D');
                  else recentForm.push('L');
            }

            return {
                  id: `team-${leagueId}-${index + 1}`,
                  name,
                  logo: `/images/teams/${name.toLowerCase().replace(/\s+/g, '-')}.png`,
                  foundedYear: Math.floor(Math.random() * 120) + 1880,
                  country: countries[Math.floor(Math.random() * countries.length)],
                  city: cities[Math.floor(Math.random() * cities.length)],
                  stadium: stadiums[Math.floor(Math.random() * stadiums.length)],
                  capacity: Math.floor(Math.random() * 60000) + 20000,
                  website: `https://${name.toLowerCase().replace(/\s+/g, '')}.com`,
                  description: `${name} is one of the most prestigious football clubs with a rich history and passionate fanbase.`,
                  stats: {
                        matchesPlayed,
                        wins,
                        draws,
                        losses,
                        goalsFor,
                        goalsAgainst,
                        points,
                        position: index + 1,
                  },
                  recentForm,
                  manager: managers[Math.floor(Math.random() * managers.length)],
                  playersCount: Math.floor(Math.random() * 10) + 25,
            };
      }).sort((a, b) => b.stats.points - a.stats.points); // Sort by points initially
};

export const useLeagueTeams = ({ leagueId, season }: UseLeagueTeamsProps): UseLeagueTeamsReturn => {
      const [teams, setTeams] = useState<TeamData[]>([]);
      const [loading, setLoading] = useState(true);
      const [error, setError] = useState<string | null>(null);
      const [searchTerm, setSearchTerm] = useState('');
      const [sortBy, setSortBy] = useState<'name' | 'foundedYear' | 'country' | 'points' | 'position'>('position');
      const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
      const [countryFilter, setCountryFilter] = useState('');

      // Simulate API call
      const fetchTeams = async () => {
            try {
                  setLoading(true);
                  setError(null);

                  // Fetch teams from real API
                  const apiTeams = await fetchTeamsFromAPI(leagueId, season);
                  setTeams(apiTeams);
            } catch (err) {
                  setError(err instanceof Error ? err.message : 'Failed to fetch teams');
            } finally {
                  setLoading(false);
            }
      };

      useEffect(() => {
            if (leagueId) {
                  // Clear any existing data first
                  setTeams([]);
                  setError(null);
                  fetchTeams();
            }
      }, [leagueId, season]);

      // Filter and sort teams
      const filteredAndSortedTeams = useMemo(() => {
            let filtered = teams;

            // Apply search filter
            if (searchTerm) {
                  filtered = filtered.filter(team =>
                        team.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        team.country.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        team.city.toLowerCase().includes(searchTerm.toLowerCase())
                  );
            }

            // Apply country filter
            if (countryFilter) {
                  filtered = filtered.filter(team => team.country === countryFilter);
            }

            // Apply sorting
            const sorted = [...filtered].sort((a, b) => {
                  let aValue: any, bValue: any;

                  switch (sortBy) {
                        case 'name':
                              aValue = a.name.toLowerCase();
                              bValue = b.name.toLowerCase();
                              break;
                        case 'foundedYear':
                              aValue = a.foundedYear;
                              bValue = b.foundedYear;
                              break;
                        case 'country':
                              aValue = a.country.toLowerCase();
                              bValue = b.country.toLowerCase();
                              break;
                        case 'points':
                              aValue = a.stats.points;
                              bValue = b.stats.points;
                              break;
                        case 'position':
                              aValue = a.stats.position || 999;
                              bValue = b.stats.position || 999;
                              break;
                        default:
                              return 0;
                  }

                  if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
                  if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
                  return 0;
            });

            return sorted;
      }, [teams, searchTerm, countryFilter, sortBy, sortOrder]);

      // Get unique countries
      const countries = useMemo(() => {
            const uniqueCountries = Array.from(new Set(teams.map(team => team.country)));
            return uniqueCountries.sort();
      }, [teams]);

      // Calculate statistics
      const stats = useMemo(() => {
            const totalTeams = teams.length;
            const totalCountries = countries.length;
            const averageFoundedYear = totalTeams > 0
                  ? Math.round(teams.reduce((sum, team) => sum + team.foundedYear, 0) / totalTeams)
                  : 0;
            const totalMatches = teams.reduce((sum, team) => sum + team.stats.matchesPlayed, 0);

            return {
                  totalTeams,
                  totalCountries,
                  averageFoundedYear,
                  totalMatches,
            };
      }, [teams, countries]);

      const refreshTeams = async () => {
            await fetchTeams();
      };

      return {
            teams,
            loading,
            error,
            searchTerm,
            setSearchTerm,
            sortBy,
            setSortBy,
            sortOrder,
            setSortOrder,
            countryFilter,
            setCountryFilter,
            filteredAndSortedTeams,
            countries,
            stats,
            refreshTeams,
      };
};

export default useLeagueTeams;
