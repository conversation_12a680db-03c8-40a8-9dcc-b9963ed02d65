import { NextRequest, NextResponse } from 'next/server';

export const dynamic = 'force-dynamic';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000';

// Helper function to get auth token from request
function getAuthToken(request: NextRequest): string | null {
  const authHeader = request.headers.get('authorization');
  return authHeader?.startsWith('Bearer ') ? authHeader : null;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Forward all query parameters
    const params = new URLSearchParams();
    searchParams.forEach((value, key) => {
      params.append(key, value);
    });

    console.log('🔄 Proxying leagues GET request:', `${API_BASE_URL}/football/leagues?${params.toString()}`);

    const response = await fetch(`${API_BASE_URL}/football/leagues?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...(request.headers.get('authorization') && {
          'Authorization': request.headers.get('authorization')!
        })
      },
    });

    if (!response.ok) {
      console.error('❌ API Error:', response.status, response.statusText);
      return NextResponse.json(
        {
          error: 'Failed to fetch leagues',
          status: response.status,
          message: response.statusText
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log('✅ Leagues fetched successfully:', data.meta);

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('❌ Proxy Error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error.message
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const authToken = getAuthToken(request);
    if (!authToken) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await request.json();
    console.log('🔄 Proxying leagues POST request:', `${API_BASE_URL}/football/leagues`);
    console.log('📝 Request body:', body);

    const response = await fetch(`${API_BASE_URL}/football/leagues`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authToken,
      },
      body: JSON.stringify(body),
    });

    console.log('📡 Real API response status:', response.status);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ message: response.statusText }));
      console.error('❌ API Error:', response.status, errorData);
      return NextResponse.json(
        {
          error: 'Failed to create league',
          status: response.status,
          message: errorData.message || response.statusText,
          details: errorData
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log('✅ League created successfully:', data);

    return NextResponse.json(data);
  } catch (error: any) {
    console.error('❌ Proxy Error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error.message
      },
      { status: 500 }
    );
  }
}
