'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
      Trophy,
      TrendingUp,
      TrendingDown,
      Minus,
      RefreshCw,
      Crown,
      Target,
      Users,
      BarChart3,
      AlertTriangle,
} from 'lucide-react';
import { useLeagueStandings, StandingData } from '@/lib/hooks/leagues/useLeagueStandings';
import { buildTeamLogoUrl } from '@/lib/utils/image';

interface LeagueStandingsProps {
      leagueId: string;
      season?: number;
      className?: string;
}

export const LeagueStandings: React.FC<LeagueStandingsProps> = ({
      leagueId,
      season,
      className = '',
}) => {
      const {
            standings,
            loading,
            error,
            refreshStandings,
            stats,
      } = useLeagueStandings({ leagueId, season });

      // Debug logging
      console.log('🔍 LeagueStandings component render:', {
            leagueId,
            season,
            standingsCount: standings.length,
            loading,
            error,
            firstTeam: standings[0]?.team?.name,
            isFallbackData: standings[0]?.team?.name === 'Manchester United' // Check if using fallback
      });

      // Check if we're displaying fallback data
      const isFallbackData = standings.length > 0 && standings[0]?.team?.name === 'Manchester United';
      if (isFallbackData) {
            console.warn('⚠️ League Table is displaying FALLBACK data instead of real API data!');
      }

      // Team Logo Component with CDN support and fallback
      const TeamLogo = ({ team }: { team: StandingData['team'] }) => {
            const logoUrl = buildTeamLogoUrl(team.logo);

            if (!logoUrl) {
                  return (
                        <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xs font-bold">
                              {team.name.slice(0, 2).toUpperCase()}
                        </div>
                  );
            }

            return (
                  <img
                        src={logoUrl}
                        alt={`${team.name} logo`}
                        className="w-8 h-8 object-contain"
                        onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.style.display = 'none';
                              const fallback = target.nextElementSibling as HTMLElement;
                              if (fallback) fallback.style.display = 'flex';
                        }}
                  />
            );
      };

      // Form indicator component
      const FormIndicator = ({ form }: { form: ('W' | 'L' | 'D')[] }) => (
            <div className="flex space-x-1">
                  {form.map((result, index) => (
                        <div
                              key={index}
                              className={`w-2 h-2 rounded-full ${result === 'W'
                                    ? 'bg-green-500'
                                    : result === 'L'
                                          ? 'bg-red-500'
                                          : 'bg-yellow-500'
                                    }`}
                              title={result === 'W' ? 'Win' : result === 'L' ? 'Loss' : 'Draw'}
                        />
                  ))}
            </div>
      );

      // Position change indicator
      const PositionIndicator = ({ position }: { position: number }) => {
            if (position === 1) {
                  return <Crown className="h-4 w-4 text-yellow-500" />;
            }
            if (position <= 4) {
                  return <TrendingUp className="h-4 w-4 text-green-500" />;
            }
            if (position >= standings.length - 2) {
                  return <TrendingDown className="h-4 w-4 text-red-500" />;
            }
            return <Minus className="h-4 w-4 text-gray-400" />;
      };

      if (loading) {
            return (
                  <Card className={className}>
                        <CardHeader>
                              <CardTitle className="flex items-center space-x-2">
                                    <Trophy className="h-5 w-5" />
                                    <span>League Table</span>
                              </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-3">
                              {[...Array(10)].map((_, i) => (
                                    <div key={i} className="flex items-center space-x-3">
                                          <Skeleton className="w-6 h-6" />
                                          <Skeleton className="w-8 h-8 rounded-full" />
                                          <Skeleton className="flex-1 h-4" />
                                          <Skeleton className="w-8 h-4" />
                                          <Skeleton className="w-12 h-4" />
                                    </div>
                              ))}
                        </CardContent>
                  </Card>
            );
      }

      if (error) {
            return (
                  <Card className={className}>
                        <CardHeader>
                              <CardTitle className="flex items-center justify-between">
                                    <div className="flex items-center space-x-2">
                                          <Trophy className="h-5 w-5" />
                                          <span>League Table</span>
                                    </div>
                                    <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={refreshStandings}
                                    >
                                          <RefreshCw className="h-4 w-4" />
                                    </Button>
                              </CardTitle>
                        </CardHeader>
                        <CardContent>
                              <div className="text-center text-muted-foreground">
                                    <BarChart3 className="h-12 w-12 mx-auto mb-2 opacity-50" />
                                    <p>Unable to load standings</p>
                                    <Button
                                          variant="outline"
                                          size="sm"
                                          onClick={refreshStandings}
                                          className="mt-2"
                                    >
                                          Try Again
                                    </Button>
                              </div>
                        </CardContent>
                  </Card>
            );
      }

      return (
            <Card className={className}>
                  <CardHeader>
                        <div className="flex items-center justify-between">
                              <CardTitle className="flex items-center space-x-2">
                                    <Trophy className="h-5 w-5" />
                                    <span>League Table</span>
                                    <Badge variant="secondary" className="ml-2">
                                          {season || new Date().getFullYear()}
                                    </Badge>
                              </CardTitle>
                              <div className="flex items-center gap-2">
                                    <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => {
                                                console.log('🔄 Manual refresh triggered');
                                                refreshStandings();
                                          }}
                                          className="text-xs"
                                    >
                                          <RefreshCw className="h-4 w-4 mr-1" />
                                          Refresh
                                    </Button>
                                    <Button
                                          variant="ghost"
                                          size="sm"
                                          onClick={() => {
                                                console.log('🔄 Force clear cache and refresh');
                                                // Force clear localStorage cache if any
                                                localStorage.removeItem(`standings-${leagueId}-${season}`);
                                                // Force refresh with new timestamp
                                                window.location.reload();
                                          }}
                                          className="text-xs text-red-600"
                                    >
                                          <RefreshCw className="h-4 w-4 mr-1" />
                                          Force Refresh
                                    </Button>
                              </div>
                        </div>

                        {/* Statistics Summary */}
                        <div className="grid grid-cols-3 gap-4 mt-4 text-sm">
                              <div className="text-center">
                                    <div className="flex items-center justify-center space-x-1 text-muted-foreground">
                                          <Users className="h-3 w-3" />
                                          <span>Teams</span>
                                    </div>
                                    <div className="font-semibold">{stats.totalTeams}</div>
                              </div>
                              <div className="text-center">
                                    <div className="flex items-center justify-center space-x-1 text-muted-foreground">
                                          <Crown className="h-3 w-3" />
                                          <span>Leader</span>
                                    </div>
                                    <div className="font-semibold text-xs">
                                          {stats.topTeam?.team.name.substring(0, 10) || 'N/A'}
                                    </div>
                              </div>
                              <div className="text-center">
                                    <div className="flex items-center justify-center space-x-1 text-muted-foreground">
                                          <Target className="h-3 w-3" />
                                          <span>Avg Goals</span>
                                    </div>
                                    <div className="font-semibold">{stats.avgGoalsPerGame}</div>
                              </div>
                        </div>
                  </CardHeader>

                  <CardContent>
                        {/* Fallback Data Warning */}
                        {isFallbackData && (
                              <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                                    <div className="flex items-center space-x-2 text-yellow-800">
                                          <AlertTriangle className="h-4 w-4" />
                                          <span className="text-sm font-medium">
                                                Warning: Displaying fallback data. Real API data may not be loading correctly.
                                          </span>
                                    </div>
                              </div>
                        )}
                        {/* Table Header */}
                        <div className="grid grid-cols-12 gap-2 text-xs font-medium text-muted-foreground mb-3 px-2">
                              <div className="col-span-1 text-center">#</div>
                              <div className="col-span-4">Team</div>
                              <div className="col-span-1 text-center">P</div>
                              <div className="col-span-1 text-center">W</div>
                              <div className="col-span-1 text-center">D</div>
                              <div className="col-span-1 text-center">L</div>
                              <div className="col-span-1 text-center">GD</div>
                              <div className="col-span-1 text-center">Pts</div>
                              <div className="col-span-1 text-center">Form</div>
                        </div>

                        {/* Standings List */}
                        <div className="space-y-2">
                              {standings.map((standing, index) => (
                                    <div
                                          key={standing.team.id}
                                          className={`grid grid-cols-12 gap-2 items-center p-2 rounded-lg hover:bg-muted/50 transition-colors ${index < 4
                                                ? 'bg-green-50 border-l-4 border-green-500'
                                                : index >= standings.length - 3
                                                      ? 'bg-red-50 border-l-4 border-red-500'
                                                      : 'bg-background'
                                                }`}
                                    >
                                          {/* Position */}
                                          <div className="col-span-1 text-center font-medium">
                                                <div className="flex items-center justify-center space-x-1">
                                                      <span className="text-sm">{standing.position}</span>
                                                      <PositionIndicator position={standing.position} />
                                                </div>
                                          </div>

                                          {/* Team */}
                                          <div className="col-span-4 flex items-center space-x-2">
                                                <TeamLogo team={standing.team} />
                                                <div className="min-w-0 flex-1">
                                                      <p className="font-medium text-sm truncate">
                                                            {standing.team.name}
                                                      </p>
                                                </div>
                                          </div>

                                          {/* Played */}
                                          <div className="col-span-1 text-center text-sm">
                                                {standing.playedGames}
                                          </div>

                                          {/* Wins */}
                                          <div className="col-span-1 text-center text-sm font-medium text-green-600">
                                                {standing.wins}
                                          </div>

                                          {/* Draws */}
                                          <div className="col-span-1 text-center text-sm font-medium text-yellow-600">
                                                {standing.draws}
                                          </div>

                                          {/* Losses */}
                                          <div className="col-span-1 text-center text-sm font-medium text-red-600">
                                                {standing.losses}
                                          </div>

                                          {/* Goal Difference */}
                                          <div className={`col-span-1 text-center text-sm font-medium ${standing.goalDifference > 0
                                                ? 'text-green-600'
                                                : standing.goalDifference < 0
                                                      ? 'text-red-600'
                                                      : 'text-muted-foreground'
                                                }`}>
                                                {standing.goalDifference > 0 ? '+' : ''}{standing.goalDifference}
                                          </div>

                                          {/* Points */}
                                          <div className="col-span-1 text-center text-sm font-bold">
                                                {standing.points}
                                          </div>

                                          {/* Form */}
                                          <div className="col-span-1 flex justify-center">
                                                <FormIndicator form={standing.form} />
                                          </div>
                                    </div>
                              ))}
                        </div>

                        {/* Legend */}
                        <div className="mt-4 pt-4 border-t">
                              <div className="flex flex-wrap gap-4 text-xs text-muted-foreground">
                                    <div className="flex items-center space-x-1">
                                          <div className="w-3 h-1 bg-green-500 rounded"></div>
                                          <span>Champions League</span>
                                    </div>
                                    <div className="flex items-center space-x-1">
                                          <div className="w-3 h-1 bg-red-500 rounded"></div>
                                          <span>Relegation</span>
                                    </div>
                                    <div className="flex items-center space-x-2 ml-4">
                                          <div className="flex space-x-1">
                                                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                                <span>W</span>
                                          </div>
                                          <div className="flex space-x-1">
                                                <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                                                <span>D</span>
                                          </div>
                                          <div className="flex space-x-1">
                                                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                                                <span>L</span>
                                          </div>
                                    </div>
                              </div>
                        </div>
                  </CardContent>
            </Card>
      );
};

export default LeagueStandings;
