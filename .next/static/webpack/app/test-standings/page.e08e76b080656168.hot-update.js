"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-standings/page",{

/***/ "(app-pages-browser)/./src/lib/hooks/leagues/useLeagueStandings.ts":
/*!*****************************************************!*\
  !*** ./src/lib/hooks/leagues/useLeagueStandings.ts ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLeagueStandings: function() { return /* binding */ useLeagueStandings; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useLeagueStandings auto */ \n// Fetch standings from API\nconst fetchStandingsFromAPI = async (leagueId, season, format)=>{\n    try {\n        var _apiData_response, _apiData_response_, _apiData_response1, _apiData_response__league, _apiData_response_1, _apiData_response2, _standingsArray__team, _standingsArray_;\n        const currentSeason = season || new Date().getFullYear();\n        const formatParam = format ? \"&format=\".concat(format) : \"\";\n        console.log(\"\\uD83D\\uDD04 Fetching standings for league:\", leagueId, \"season:\", currentSeason);\n        // Use proxy endpoint through Next.js frontend with cache busting\n        const timestamp = Date.now();\n        const apiUrl = \"/api/standings?league=\".concat(leagueId, \"&season=\").concat(currentSeason).concat(formatParam, \"&_t=\").concat(timestamp);\n        console.log(\"\\uD83C\\uDF10 Making API call to:\", apiUrl);\n        const response = await fetch(apiUrl, {\n            method: \"GET\",\n            headers: {\n                \"Cache-Control\": \"no-cache, no-store, must-revalidate\",\n                \"Pragma\": \"no-cache\",\n                \"Expires\": \"0\"\n            }\n        });\n        if (!response.ok) {\n            console.error(\"❌ API Response not OK:\", response.status, response.statusText);\n            throw new Error(\"Failed to fetch standings: \".concat(response.status, \" \").concat(response.statusText));\n        }\n        const apiData = await response.json();\n        console.log(\"✅ Standings API response:\", apiData);\n        // Extract standings data from the correct path: response[0].league.standings[0]\n        let standingsArray = [];\n        if (apiData.response && Array.isArray(apiData.response) && apiData.response.length > 0) {\n            const leagueData = apiData.response[0];\n            if (leagueData.league && leagueData.league.standings && Array.isArray(leagueData.league.standings)) {\n                standingsArray = leagueData.league.standings[0] || [];\n            }\n        }\n        console.log(\"\\uD83D\\uDD0D Extracted standings data:\", {\n            hasResponse: !!apiData.response,\n            responseLength: (_apiData_response = apiData.response) === null || _apiData_response === void 0 ? void 0 : _apiData_response.length,\n            hasLeague: !!((_apiData_response1 = apiData.response) === null || _apiData_response1 === void 0 ? void 0 : (_apiData_response_ = _apiData_response1[0]) === null || _apiData_response_ === void 0 ? void 0 : _apiData_response_.league),\n            hasStandings: !!((_apiData_response2 = apiData.response) === null || _apiData_response2 === void 0 ? void 0 : (_apiData_response_1 = _apiData_response2[0]) === null || _apiData_response_1 === void 0 ? void 0 : (_apiData_response__league = _apiData_response_1.league) === null || _apiData_response__league === void 0 ? void 0 : _apiData_response__league.standings),\n            standingsLength: standingsArray.length,\n            firstTeam: (_standingsArray_ = standingsArray[0]) === null || _standingsArray_ === void 0 ? void 0 : (_standingsArray__team = _standingsArray_.team) === null || _standingsArray__team === void 0 ? void 0 : _standingsArray__team.name\n        });\n        if (!standingsArray || !Array.isArray(standingsArray) || standingsArray.length === 0) {\n            console.error(\"❌ No standings data found in API response:\", {\n                responseStructure: Object.keys(apiData),\n                standingsArray: standingsArray\n            });\n            // In development, throw error instead of using fallback\n            if (true) {\n                throw new Error(\"No standings data found. Response structure: \".concat(Object.keys(apiData).join(\", \")));\n            }\n            console.warn(\"⚠️ No standings data in API response, using fallback\");\n            return generateFallbackStandings(leagueId);\n        }\n        // Transform API data to our interface\n        console.log(\"\\uD83D\\uDD04 Transforming API data to interface, count:\", standingsArray.length);\n        const transformedData = standingsArray.map((standing, index)=>{\n            var _standing_team_id, _standing_team, _standing_team1, _standing_team2, _standing_all, _standing_all1, _standing_all2, _standing_all3, _standing_all_goals, _standing_all4, _standing_all_goals1, _standing_all5, _standing_team3, _standing_team4, _standing_team5, _standing_team6;\n            // Parse form data from API (e.g., \"WWWWW\" -> ['W', 'W', 'W', 'W', 'W'])\n            let form = [];\n            if (standing.form && typeof standing.form === \"string\") {\n                form = standing.form.split(\"\").slice(0, 5);\n            } else {\n                // Fallback to random form if not provided\n                const formResults = [\n                    \"W\",\n                    \"L\",\n                    \"D\"\n                ];\n                form = Array.from({\n                    length: 5\n                }, ()=>formResults[Math.floor(Math.random() * formResults.length)]);\n            }\n            return {\n                position: standing.rank || standing.position || index + 1,\n                team: {\n                    id: ((_standing_team = standing.team) === null || _standing_team === void 0 ? void 0 : (_standing_team_id = _standing_team.id) === null || _standing_team_id === void 0 ? void 0 : _standing_team_id.toString()) || (index + 1).toString(),\n                    name: ((_standing_team1 = standing.team) === null || _standing_team1 === void 0 ? void 0 : _standing_team1.name) || \"Team \".concat(index + 1),\n                    logo: ((_standing_team2 = standing.team) === null || _standing_team2 === void 0 ? void 0 : _standing_team2.logo) || \"\"\n                },\n                points: standing.points || 0,\n                playedGames: ((_standing_all = standing.all) === null || _standing_all === void 0 ? void 0 : _standing_all.played) || 0,\n                wins: ((_standing_all1 = standing.all) === null || _standing_all1 === void 0 ? void 0 : _standing_all1.win) || 0,\n                draws: ((_standing_all2 = standing.all) === null || _standing_all2 === void 0 ? void 0 : _standing_all2.draw) || 0,\n                losses: ((_standing_all3 = standing.all) === null || _standing_all3 === void 0 ? void 0 : _standing_all3.lose) || 0,\n                goalsFor: ((_standing_all4 = standing.all) === null || _standing_all4 === void 0 ? void 0 : (_standing_all_goals = _standing_all4.goals) === null || _standing_all_goals === void 0 ? void 0 : _standing_all_goals.for) || 0,\n                goalsAgainst: ((_standing_all5 = standing.all) === null || _standing_all5 === void 0 ? void 0 : (_standing_all_goals1 = _standing_all5.goals) === null || _standing_all_goals1 === void 0 ? void 0 : _standing_all_goals1.against) || 0,\n                goalDifference: standing.goalsDiff || 0,\n                form,\n                // Store original API fields for reference\n                externalId: (_standing_team3 = standing.team) === null || _standing_team3 === void 0 ? void 0 : _standing_team3.id,\n                teamId: (_standing_team4 = standing.team) === null || _standing_team4 === void 0 ? void 0 : _standing_team4.id,\n                teamName: (_standing_team5 = standing.team) === null || _standing_team5 === void 0 ? void 0 : _standing_team5.name,\n                teamLogo: (_standing_team6 = standing.team) === null || _standing_team6 === void 0 ? void 0 : _standing_team6.logo\n            };\n        });\n        const sortedData = transformedData.sort((a, b)=>a.position - b.position);\n        console.log(\"✅ Transformed standings data:\", sortedData.slice(0, 3).map((s)=>({\n                position: s.position,\n                teamName: s.team.name,\n                points: s.points\n            })));\n        return sortedData;\n    } catch (error) {\n        console.error(\"❌ Error fetching standings:\", error);\n        throw error;\n    }\n};\n// Generate fallback standings data for development/testing\nconst generateFallbackStandings = (leagueId)=>{\n    const teams = [\n        {\n            id: \"33\",\n            name: \"Manchester United\",\n            logo: \"https://media.api-sports.io/football/teams/33.png\"\n        },\n        {\n            id: \"50\",\n            name: \"Manchester City\",\n            logo: \"https://media.api-sports.io/football/teams/50.png\"\n        },\n        {\n            id: \"42\",\n            name: \"Arsenal\",\n            logo: \"https://media.api-sports.io/football/teams/42.png\"\n        },\n        {\n            id: \"40\",\n            name: \"Liverpool\",\n            logo: \"https://media.api-sports.io/football/teams/40.png\"\n        },\n        {\n            id: \"49\",\n            name: \"Chelsea\",\n            logo: \"https://media.api-sports.io/football/teams/49.png\"\n        },\n        {\n            id: \"47\",\n            name: \"Tottenham\",\n            logo: \"https://media.api-sports.io/football/teams/47.png\"\n        },\n        {\n            id: \"34\",\n            name: \"Newcastle\",\n            logo: \"https://media.api-sports.io/football/teams/34.png\"\n        },\n        {\n            id: \"66\",\n            name: \"Aston Villa\",\n            logo: \"https://media.api-sports.io/football/teams/66.png\"\n        },\n        {\n            id: \"51\",\n            name: \"Brighton\",\n            logo: \"https://media.api-sports.io/football/teams/51.png\"\n        },\n        {\n            id: \"39\",\n            name: \"Wolves\",\n            logo: \"https://media.api-sports.io/football/teams/39.png\"\n        }\n    ];\n    return teams.map((team, index)=>{\n        const played = 20 + Math.floor(Math.random() * 10);\n        const wins = Math.floor(Math.random() * played * 0.7);\n        const losses = Math.floor(Math.random() * (played - wins) * 0.6);\n        const draws = played - wins - losses;\n        const goalsFor = wins * 2 + draws + Math.floor(Math.random() * 10);\n        const goalsAgainst = losses * 2 + Math.floor(Math.random() * goalsFor * 0.8);\n        // Generate realistic form (last 5 matches)\n        const formResults = [\n            \"W\",\n            \"L\",\n            \"D\"\n        ];\n        const form = Array.from({\n            length: 5\n        }, ()=>formResults[Math.floor(Math.random() * formResults.length)]);\n        return {\n            position: index + 1,\n            team,\n            points: wins * 3 + draws,\n            playedGames: played,\n            wins,\n            draws,\n            losses,\n            goalsFor,\n            goalsAgainst,\n            goalDifference: goalsFor - goalsAgainst,\n            form,\n            externalId: parseInt(team.id),\n            teamId: parseInt(team.id),\n            teamName: team.name,\n            teamLogo: team.logo\n        };\n    }).sort((a, b)=>b.points - a.points || b.goalDifference - a.goalDifference);\n};\nconst useLeagueStandings = (param)=>{\n    let { leagueId, season, format = \"external\" } = param;\n    const [standings, setStandings] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    console.log(\"\\uD83C\\uDFD7️ useLeagueStandings hook initialized:\", {\n        leagueId,\n        season,\n        format\n    });\n    const refreshStandings = async ()=>{\n        try {\n            var _standingsData__team, _standingsData_;\n            setLoading(true);\n            setError(null);\n            const standingsData = await fetchStandingsFromAPI(leagueId, season, format);\n            console.log(\"\\uD83C\\uDFAF Setting standings data in hook:\", standingsData.length, \"teams\");\n            console.log(\"\\uD83C\\uDFAF First team in standings:\", (_standingsData_ = standingsData[0]) === null || _standingsData_ === void 0 ? void 0 : (_standingsData__team = _standingsData_.team) === null || _standingsData__team === void 0 ? void 0 : _standingsData__team.name);\n            setStandings(standingsData);\n        } catch (err) {\n            console.error(\"❌ Error fetching standings:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to fetch standings\");\n            // In development, don't use fallback to debug the real issue\n            if (true) {\n                console.error(\"\\uD83D\\uDEA8 DEVELOPMENT MODE: Not using fallback data to debug API issue\");\n                setStandings([]); // Show empty instead of fallback\n            } else {}\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (leagueId) {\n            console.log(\"\\uD83D\\uDD04 useLeagueStandings useEffect triggered:\", {\n                leagueId,\n                season,\n                format\n            });\n            // Clear any existing data first\n            setStandings([]);\n            setError(null);\n            refreshStandings();\n        }\n    }, [\n        leagueId,\n        season,\n        format\n    ]);\n    // Calculate statistics\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const totalTeams = standings.length;\n        const topTeam = standings.length > 0 ? standings[0] : null;\n        const totalGames = standings.reduce((sum, team)=>sum + team.playedGames, 0);\n        const totalGoals = standings.reduce((sum, team)=>sum + team.goalsFor, 0);\n        const avgGoalsPerGame = totalGames > 0 ? Math.round(totalGoals / totalGames * 100) / 100 : 0;\n        return {\n            totalTeams,\n            topTeam,\n            avgGoalsPerGame\n        };\n    }, [\n        standings\n    ]);\n    return {\n        standings,\n        loading,\n        error,\n        refreshStandings,\n        stats\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/hooks/leagues/useLeagueStandings.ts\n"));

/***/ })

});