'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Input } from '@/components/ui/input';
import {
      Users,
      Search,
      MoreHorizontal,
      Eye,
      Trophy,
      TrendingUp,
      MapPin,
      Calendar,
      ArrowRight,
      Filter,
      Grid3X3,
      List,
      ChevronDown,
      ChevronUp,
} from 'lucide-react';
import { useLeagueTeams } from '@/lib/hooks/leagues/useLeagueTeams';
import { buildTeamLogoUrl } from '@/lib/utils/image';
import TeamQuickViewModal from './TeamQuickViewModal';

interface LeagueTeamsSectionProps {
      leagueId: string;
      season?: number;
      className?: string;
}

const LeagueTeamsSection: React.FC<LeagueTeamsSectionProps> = ({
      leagueId,
      season,
      className = '',
}) => {
      const router = useRouter();
      const [searchTerm, setSearchTerm] = useState('');
      const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
      const [selectedTeam, setSelectedTeam] = useState<any>(null);
      const [isModalOpen, setIsModalOpen] = useState(false);
      const [showAll, setShowAll] = useState(false);

      // Number of teams to show initially
      const INITIAL_TEAMS_COUNT = 6;

      const {
            teams,
            loading,
            error,
            stats,
            refreshTeams,
      } = useLeagueTeams({ leagueId, season });

      // Debug logging
      console.log('🔍 LeagueTeamsSection component render:', {
            leagueId,
            season,
            teamsCount: teams.length,
            loading,
            error,
            firstTeam: teams[0]?.name
      });

      const filteredTeams = teams.filter(team =>
            team.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            team.stadium?.toLowerCase().includes(searchTerm.toLowerCase()) ||
            team.city?.toLowerCase().includes(searchTerm.toLowerCase())
      );

      // Determine which teams to display
      const teamsToDisplay = showAll || searchTerm
            ? filteredTeams
            : filteredTeams.slice(0, INITIAL_TEAMS_COUNT);

      const hasMoreTeams = filteredTeams.length > INITIAL_TEAMS_COUNT;

      const handleTeamClick = (team: any) => {
            setSelectedTeam(team);
            setIsModalOpen(true);
      };

      const handleViewAllTeams = () => {
            // Navigate to teams page with league filter
            router.push(`/dashboard/teams?league=${leagueId}&season=${season || new Date().getFullYear()}`);
      };

      const handleToggleShowMore = () => {
            setShowAll(!showAll);
      };

      if (loading) {
            return (
                  <Card className={className}>
                        <CardHeader>
                              <div className="flex items-center justify-between">
                                    <CardTitle className="flex items-center space-x-2">
                                          <Users className="w-5 h-5" />
                                          <span>League Teams</span>
                                    </CardTitle>
                                    <Skeleton className="h-8 w-24" />
                              </div>
                        </CardHeader>
                        <CardContent>
                              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    {[...Array(6)].map((_, i) => (
                                          <Skeleton key={i} className="h-32" />
                                    ))}
                              </div>
                        </CardContent>
                  </Card>
            );
      }

      if (error) {
            return (
                  <Card className={className}>
                        <CardHeader>
                              <CardTitle className="flex items-center space-x-2">
                                    <Users className="w-5 h-5" />
                                    <span>League Teams</span>
                              </CardTitle>
                        </CardHeader>
                        <CardContent>
                              <div className="text-center py-8">
                                    <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                    <p className="text-gray-500">Failed to load teams data</p>
                                    <Button
                                          variant="outline"
                                          className="mt-4"
                                          onClick={refreshTeams}
                                    >
                                          Try Again
                                    </Button>
                              </div>
                        </CardContent>
                  </Card>
            );
      }

      return (
            <>
                  <Card className={className}>
                        <CardHeader>
                              <div className="flex items-center justify-between">
                                    <CardTitle className="flex items-center space-x-2">
                                          <Users className="w-5 h-5" />
                                          <span>League Teams</span>
                                          <Badge variant="secondary" className="ml-2">
                                                {showAll || searchTerm ? filteredTeams.length : Math.min(filteredTeams.length, INITIAL_TEAMS_COUNT)}
                                                {!showAll && !searchTerm && hasMoreTeams && ` of ${filteredTeams.length}`} teams
                                          </Badge>
                                    </CardTitle>
                                    <div className="flex items-center space-x-2">
                                          <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                                          >
                                                {viewMode === 'grid' ? (
                                                      <List className="w-4 h-4" />
                                                ) : (
                                                      <Grid3X3 className="w-4 h-4" />
                                                )}
                                          </Button>
                                          <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={refreshTeams}
                                          >
                                                <TrendingUp className="w-4 h-4" />
                                          </Button>
                                    </div>
                              </div>
                        </CardHeader>
                        <CardContent className="space-y-4">
                              {/* Search and Filters */}
                              <div className="flex items-center space-x-4">
                                    <div className="relative flex-1">
                                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />                                    <Input
                                                placeholder="Search teams by name, stadium, or city..."
                                                value={searchTerm}
                                                onChange={(e) => setSearchTerm(e.target.value)}
                                                className="pl-10"
                                          />
                                    </div>
                                    <Button variant="outline" size="sm">
                                          <Filter className="w-4 h-4 mr-2" />
                                          Filter
                                    </Button>
                              </div>

                              {/* Teams Grid/List */}
                              {filteredTeams.length === 0 ? (
                                    <div className="text-center py-8">
                                          <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                          <p className="text-gray-500">
                                                {searchTerm ? 'No teams found matching your search' : 'No teams available'}
                                          </p>
                                    </div>
                              ) : (
                                    <div className={
                                          viewMode === 'grid'
                                                ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
                                                : "space-y-3"
                                    }>
                                          {teamsToDisplay.map((team) => (
                                                <div
                                                      key={team.id}
                                                      className={`
                                                            border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer
                                                            ${viewMode === 'list' ? 'flex items-center space-x-4' : ''}
                                                      `}
                                                      onClick={() => handleTeamClick(team)}
                                                >
                                                      <div className={`
                                                            ${viewMode === 'list' ? 'flex-shrink-0' : 'text-center mb-3'}
                                                      `}>
                                                            {team.logo && buildTeamLogoUrl(team.logo) ? (
                                                                  <img
                                                                        src={buildTeamLogoUrl(team.logo) || ''}
                                                                        alt={team.name}
                                                                        className={`
                                                                              object-contain
                                                                              ${viewMode === 'list' ? 'w-12 h-12' : 'w-16 h-16 mx-auto'}
                                                                        `}
                                                                        onError={(e) => {
                                                                              const target = e.target as HTMLImageElement;
                                                                              target.style.display = 'none';
                                                                        }}
                                                                  />
                                                            ) : (
                                                                  <div className={`
                                                                        bg-gray-100 rounded-lg flex items-center justify-center
                                                                        ${viewMode === 'list' ? 'w-12 h-12' : 'w-16 h-16 mx-auto'}
                                                                  `}>
                                                                        <Trophy className="w-6 h-6 text-gray-400" />
                                                                  </div>
                                                            )}
                                                      </div>

                                                      <div className={`
                                                            ${viewMode === 'list' ? 'flex-1' : ''}
                                                      `}>
                                                            <h4 className={`
                                                                  font-medium text-gray-900
                                                                  ${viewMode === 'list' ? 'text-base' : 'text-sm mb-1'}
                                                            `}>
                                                                  {team.name}
                                                            </h4>

                                                            {team.stadium && (
                                                                  <div className={`
                                                                        flex items-center text-gray-500 text-xs
                                                                        ${viewMode === 'list' ? 'mt-1' : 'justify-center mt-2'}
                                                                  `}>
                                                                        <MapPin className="w-3 h-3 mr-1" />
                                                                        <span>{team.stadium}</span>
                                                                  </div>
                                                            )}

                                                            {team.foundedYear && (
                                                                  <div className={`
                                                                        flex items-center text-gray-500 text-xs mt-1
                                                                        ${viewMode === 'list' ? '' : 'justify-center'}
                                                                  `}>
                                                                        <Calendar className="w-3 h-3 mr-1" />
                                                                        <span>Founded {team.foundedYear}</span>
                                                                  </div>
                                                            )}

                                                            {viewMode === 'list' && (
                                                                  <div className="flex items-center justify-between mt-2">
                                                                        <div className="flex items-center space-x-2">
                                                                              {team.country && (
                                                                                    <Badge variant="secondary" className="text-xs">
                                                                                          {team.country}
                                                                                    </Badge>
                                                                              )}
                                                                        </div>
                                                                        <Button variant="ghost" size="sm">
                                                                              <Eye className="w-4 h-4" />
                                                                        </Button>
                                                                  </div>
                                                            )}
                                                      </div>

                                                      {viewMode === 'grid' && (
                                                            <div className="flex items-center justify-center mt-3">
                                                                  <Button variant="ghost" size="sm">
                                                                        <Eye className="w-4 h-4 mr-1" />
                                                                        View Details
                                                                  </Button>
                                                            </div>
                                                      )}
                                                </div>
                                          ))}
                                    </div>
                              )}

                              {/* Action Buttons */}
                              {filteredTeams.length > 0 && (
                                    <div className="flex items-center justify-center gap-3 pt-4 border-t">
                                          {/* Show More/Less Button */}
                                          {hasMoreTeams && !searchTerm && (
                                                <Button
                                                      variant="outline"
                                                      onClick={handleToggleShowMore}
                                                      className="flex items-center gap-2"
                                                >
                                                      {showAll ? (
                                                            <>
                                                                  <ChevronUp className="w-4 h-4" />
                                                                  Show Less
                                                            </>
                                                      ) : (
                                                            <>
                                                                  <ChevronDown className="w-4 h-4" />
                                                                  Show More ({filteredTeams.length - INITIAL_TEAMS_COUNT} more)
                                                            </>
                                                      )}
                                                </Button>
                                          )}

                                          {/* View All Teams Button */}
                                          <Button
                                                variant="default"
                                                onClick={handleViewAllTeams}
                                                className="flex items-center gap-2"
                                          >
                                                <Users className="w-4 h-4" />
                                                View All Teams
                                                <ArrowRight className="w-4 h-4" />
                                          </Button>
                                    </div>
                              )}
                        </CardContent>
                  </Card>

                  {/* Team Quick View Modal */}
                  <TeamQuickViewModal
                        team={selectedTeam}
                        leagueId={leagueId}
                        season={season}
                        isOpen={isModalOpen}
                        onClose={() => {
                              setIsModalOpen(false);
                              setSelectedTeam(null);
                        }}
                  />
            </>
      );
};

export default LeagueTeamsSection;