import { apiClient } from './client';
import { League, PaginatedResponse } from '@/lib/types/api';

// Helper function to get auth token
const getAuthToken = (): string | null => {
  if (typeof window !== 'undefined') {
    try {
      const authStorage = localStorage.getItem('auth-storage');
      if (authStorage) {
        const parsed = JSON.parse(authStorage);
        return parsed.state?.accessToken || null;
      }
    } catch (error) {
      console.warn('Failed to parse auth storage:', error);
    }
    return localStorage.getItem('accessToken');
  }
  return null;
};

export interface LeagueFilters {
  page?: number;
  limit?: number;
  country?: string;
  active?: boolean;
  search?: string;
  season?: number;
}

export interface CreateLeagueData {
  externalId?: number;
  name: string;
  country: string;
  logo?: string;
  flag?: string;
  season: number;
  active: boolean;
  type: string;
  isHot?: boolean;
}

export interface UpdateLeagueData {
  name?: string;
  country?: string;
  logo?: string;
  flag?: string;
  season?: number;
  active?: boolean;
  type?: string;
  isHot?: boolean;
}

export const leaguesApi = {
  // Public endpoint via proxy
  getLeagues: async (filters: LeagueFilters = {}): Promise<PaginatedResponse<League>> => {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });

    const response = await fetch(`/api/leagues?${params.toString()}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to fetch leagues');
    }

    return await response.json();
  },

  // Requires authentication
  getLeagueById: async (externalId: number, season?: number): Promise<League> => {
    // Use the new URL format with season: {externalId}-{season}
    const urlParam = season ? `${externalId}-${season}` : externalId.toString();
    const response = await fetch(`/api/leagues/${urlParam}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to fetch league ${externalId}`);
    }

    return await response.json();
  },

  // Editor+ access required
  createLeague: async (data: CreateLeagueData): Promise<League> => {
    const response = await apiClient.post<League>('/api/leagues', data);
    return response;
  },

  // Editor+ access required
  updateLeague: async (externalId: number, data: UpdateLeagueData, season?: number): Promise<League> => {
    const token = getAuthToken();
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    // First, get the league to find its internal ID
    const league = await leaguesApi.getLeagueById(externalId, season);
    if (!league || !league.id) {
      throw new Error(`League not found: ${externalId}${season ? `-${season}` : ''}`);
    }

    // Use the internal ID for the update call
    const response = await fetch(`/api/leagues/${league.id}`, {
      method: 'PATCH',
      headers,
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || `Failed to update league ${externalId}`);
    }

    return await response.json();
  },

  // Admin access required
  deleteLeague: async (externalId: number, season?: number): Promise<void> => {
    // First, get the league to find its internal ID
    const league = await leaguesApi.getLeagueById(externalId, season);
    if (!league || !league.id) {
      throw new Error(`League not found: ${externalId}${season ? `-${season}` : ''}`);
    }

    // Use the internal ID for the delete call
    await apiClient.delete(`/football/leagues/${league.id}`);
  },

  // Helper methods for common operations
  getActiveLeagues: async (): Promise<PaginatedResponse<League>> => {
    return leaguesApi.getLeagues({ active: true });
  },

  getLeaguesByCountry: async (country: string): Promise<PaginatedResponse<League>> => {
    return leaguesApi.getLeagues({ country });
  },

  toggleLeagueStatus: async (externalId: number, active: boolean, season?: number): Promise<League> => {
    return leaguesApi.updateLeague(externalId, { active }, season);
  },
};
