"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/leagues/[id]/page",{

/***/ "(app-pages-browser)/./src/components/leagues/detail/LeagueStandings.tsx":
/*!***********************************************************!*\
  !*** ./src/components/leagues/detail/LeagueStandings.tsx ***!
  \***********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LeagueStandings: function() { return /* binding */ LeagueStandings; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Crown_Minus_RefreshCw_Target_TrendingDown_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Crown,Minus,RefreshCw,Target,TrendingDown,TrendingUp,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Crown_Minus_RefreshCw_Target_TrendingDown_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Crown,Minus,RefreshCw,Target,TrendingDown,TrendingUp,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Crown_Minus_RefreshCw_Target_TrendingDown_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Crown,Minus,RefreshCw,Target,TrendingDown,TrendingUp,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-down.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Crown_Minus_RefreshCw_Target_TrendingDown_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Crown,Minus,RefreshCw,Target,TrendingDown,TrendingUp,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/minus.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Crown_Minus_RefreshCw_Target_TrendingDown_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Crown,Minus,RefreshCw,Target,TrendingDown,TrendingUp,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Crown_Minus_RefreshCw_Target_TrendingDown_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Crown,Minus,RefreshCw,Target,TrendingDown,TrendingUp,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Crown_Minus_RefreshCw_Target_TrendingDown_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Crown,Minus,RefreshCw,Target,TrendingDown,TrendingUp,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Crown_Minus_RefreshCw_Target_TrendingDown_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Crown,Minus,RefreshCw,Target,TrendingDown,TrendingUp,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Crown_Minus_RefreshCw_Target_TrendingDown_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Crown,Minus,RefreshCw,Target,TrendingDown,TrendingUp,Trophy,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _lib_hooks_leagues_useLeagueStandings__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/hooks/leagues/useLeagueStandings */ \"(app-pages-browser)/./src/lib/hooks/leagues/useLeagueStandings.ts\");\n/* harmony import */ var _lib_utils_image__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils/image */ \"(app-pages-browser)/./src/lib/utils/image.ts\");\n/* __next_internal_client_entry_do_not_use__ LeagueStandings,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nconst LeagueStandings = (param)=>{\n    let { leagueId, season, className = \"\" } = param;\n    var _standings__team, _standings_, _standings__team1, _standings_1, _standings__team2, _standings_2, _stats_topTeam;\n    _s();\n    const { standings, loading, error, refreshStandings, stats } = (0,_lib_hooks_leagues_useLeagueStandings__WEBPACK_IMPORTED_MODULE_6__.useLeagueStandings)({\n        leagueId,\n        season\n    });\n    // Debug logging\n    console.log(\"\\uD83D\\uDD0D LeagueStandings component render:\", {\n        leagueId,\n        season,\n        standingsCount: standings.length,\n        loading,\n        error,\n        firstTeam: (_standings_ = standings[0]) === null || _standings_ === void 0 ? void 0 : (_standings__team = _standings_.team) === null || _standings__team === void 0 ? void 0 : _standings__team.name,\n        isFallbackData: ((_standings_1 = standings[0]) === null || _standings_1 === void 0 ? void 0 : (_standings__team1 = _standings_1.team) === null || _standings__team1 === void 0 ? void 0 : _standings__team1.name) === \"Manchester United\" // Check if using fallback\n    });\n    // Check if we're displaying fallback data\n    const isFallbackData = standings.length > 0 && ((_standings_2 = standings[0]) === null || _standings_2 === void 0 ? void 0 : (_standings__team2 = _standings_2.team) === null || _standings__team2 === void 0 ? void 0 : _standings__team2.name) === \"Manchester United\";\n    if (isFallbackData) {\n        console.warn(\"⚠️ League Table is displaying FALLBACK data instead of real API data!\");\n    }\n    // Team Logo Component with CDN support and fallback\n    const TeamLogo = (param)=>{\n        let { team } = param;\n        const logoUrl = (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_7__.buildTeamLogoUrl)(team.logo);\n        if (!logoUrl) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xs font-bold\",\n                children: team.name.slice(0, 2).toUpperCase()\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                lineNumber: 64,\n                columnNumber: 25\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n            src: logoUrl,\n            alt: \"\".concat(team.name, \" logo\"),\n            className: \"w-8 h-8 object-contain\",\n            onError: (e)=>{\n                const target = e.target;\n                target.style.display = \"none\";\n                const fallback = target.nextElementSibling;\n                if (fallback) fallback.style.display = \"flex\";\n            }\n        }, void 0, false, {\n            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n            lineNumber: 71,\n            columnNumber: 19\n        }, undefined);\n    };\n    // Form indicator component\n    const FormIndicator = (param)=>{\n        let { form } = param;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex space-x-1\",\n            children: form.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-2 h-2 rounded-full \".concat(result === \"W\" ? \"bg-green-500\" : result === \"L\" ? \"bg-red-500\" : \"bg-yellow-500\"),\n                    title: result === \"W\" ? \"Win\" : result === \"L\" ? \"Loss\" : \"Draw\"\n                }, index, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 25\n                }, undefined))\n        }, void 0, false, {\n            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n            lineNumber: 87,\n            columnNumber: 13\n        }, undefined);\n    };\n    // Position change indicator\n    const PositionIndicator = (param)=>{\n        let { position } = param;\n        if (position === 1) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Crown_Minus_RefreshCw_Target_TrendingDown_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-4 w-4 text-yellow-500\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                lineNumber: 106,\n                columnNumber: 26\n            }, undefined);\n        }\n        if (position <= 4) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Crown_Minus_RefreshCw_Target_TrendingDown_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-4 w-4 text-green-500\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                lineNumber: 109,\n                columnNumber: 26\n            }, undefined);\n        }\n        if (position >= standings.length - 2) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Crown_Minus_RefreshCw_Target_TrendingDown_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-4 w-4 text-red-500\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                lineNumber: 112,\n                columnNumber: 26\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Crown_Minus_RefreshCw_Target_TrendingDown_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n            className: \"h-4 w-4 text-gray-400\"\n        }, void 0, false, {\n            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n            lineNumber: 114,\n            columnNumber: 20\n        }, undefined);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            className: className,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Crown_Minus_RefreshCw_Target_TrendingDown_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"h-5 w-5\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 37\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"League Table\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 37\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 31\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 25\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"space-y-3\",\n                    children: [\n                        ...Array(10)\n                    ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 43\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                    className: \"w-8 h-8 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 43\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                    className: \"flex-1 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 43\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                    className: \"w-8 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 43\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_5__.Skeleton, {\n                                    className: \"w-12 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 43\n                                }, undefined)\n                            ]\n                        }, i, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 37\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 25\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n            lineNumber: 119,\n            columnNumber: 19\n        }, undefined);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n            className: className,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Crown_Minus_RefreshCw_Target_TrendingDown_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 43\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"League Table\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 43\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 37\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                onClick: refreshStandings,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Crown_Minus_RefreshCw_Target_TrendingDown_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 43\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 37\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 31\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                    lineNumber: 144,\n                    columnNumber: 25\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Crown_Minus_RefreshCw_Target_TrendingDown_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"h-12 w-12 mx-auto mb-2 opacity-50\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 37\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Unable to load standings\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 37\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: refreshStandings,\n                                className: \"mt-2\",\n                                children: \"Try Again\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                lineNumber: 163,\n                                columnNumber: 37\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 31\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 25\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n            lineNumber: 143,\n            columnNumber: 19\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: className,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Crown_Minus_RefreshCw_Target_TrendingDown_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"League Table\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"ml-2\",\n                                        children: season || new Date().getFullYear()\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 31\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>{\n                                            console.log(\"\\uD83D\\uDD04 Manual refresh triggered\");\n                                            refreshStandings();\n                                        },\n                                        className: \"text-xs\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Crown_Minus_RefreshCw_Target_TrendingDown_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 43\n                                            }, undefined),\n                                            \"Refresh\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>{\n                                            console.log(\"\\uD83D\\uDD04 Force clear cache and refresh\");\n                                            // Force clear localStorage cache if any\n                                            localStorage.removeItem(\"standings-\".concat(leagueId, \"-\").concat(season));\n                                            // Force refresh with new timestamp\n                                            window.location.reload();\n                                        },\n                                        className: \"text-xs text-red-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Crown_Minus_RefreshCw_Target_TrendingDown_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 43\n                                            }, undefined),\n                                            \"Force Refresh\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 31\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 25\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-3 gap-4 mt-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-1 text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Crown_Minus_RefreshCw_Target_TrendingDown_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 43\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Teams\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 43\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold\",\n                                        children: stats.totalTeams\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 31\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-1 text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Crown_Minus_RefreshCw_Target_TrendingDown_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 43\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Leader\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 43\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold text-xs\",\n                                        children: ((_stats_topTeam = stats.topTeam) === null || _stats_topTeam === void 0 ? void 0 : _stats_topTeam.team.name.substring(0, 10)) || \"N/A\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 31\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-1 text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Crown_Minus_RefreshCw_Target_TrendingDown_TrendingUp_Trophy_Users_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 43\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Avg Goals\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 43\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold\",\n                                        children: stats.avgGoalsPerGame\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 31\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 25\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                lineNumber: 179,\n                columnNumber: 19\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-12 gap-2 text-xs font-medium text-muted-foreground mb-3 px-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-1 text-center\",\n                                children: \"#\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 31\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-4\",\n                                children: \"Team\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 31\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-1 text-center\",\n                                children: \"P\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 31\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-1 text-center\",\n                                children: \"W\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 31\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-1 text-center\",\n                                children: \"D\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 31\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-1 text-center\",\n                                children: \"L\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 31\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-1 text-center\",\n                                children: \"GD\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 31\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-1 text-center\",\n                                children: \"Pts\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 31\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"col-span-1 text-center\",\n                                children: \"Form\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 31\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 25\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: standings.map((standing, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-12 gap-2 items-center p-2 rounded-lg hover:bg-muted/50 transition-colors \".concat(index < 4 ? \"bg-green-50 border-l-4 border-green-500\" : index >= standings.length - 3 ? \"bg-red-50 border-l-4 border-red-500\" : \"bg-background\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-1 text-center font-medium\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-center space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm\",\n                                                    children: standing.position\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                                    lineNumber: 276,\n                                                    columnNumber: 55\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PositionIndicator, {\n                                                    position: standing.position\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 55\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 49\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 43\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-4 flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TeamLogo, {\n                                                team: standing.team\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 49\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"min-w-0 flex-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"font-medium text-sm truncate\",\n                                                    children: standing.team.name\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 55\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 49\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 43\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-1 text-center text-sm\",\n                                        children: standing.playedGames\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 43\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-1 text-center text-sm font-medium text-green-600\",\n                                        children: standing.wins\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 43\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-1 text-center text-sm font-medium text-yellow-600\",\n                                        children: standing.draws\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 43\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-1 text-center text-sm font-medium text-red-600\",\n                                        children: standing.losses\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 43\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-1 text-center text-sm font-medium \".concat(standing.goalDifference > 0 ? \"text-green-600\" : standing.goalDifference < 0 ? \"text-red-600\" : \"text-muted-foreground\"),\n                                        children: [\n                                            standing.goalDifference > 0 ? \"+\" : \"\",\n                                            standing.goalDifference\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 43\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-1 text-center text-sm font-bold\",\n                                        children: standing.points\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 43\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"col-span-1 flex justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FormIndicator, {\n                                            form: standing.form\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 49\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 43\n                                    }, undefined)\n                                ]\n                            }, standing.team.id, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 37\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                        lineNumber: 262,\n                        columnNumber: 25\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 pt-4 border-t\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-4 text-xs text-muted-foreground\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-1 bg-green-500 rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Champions League\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 43\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-3 h-1 bg-red-500 rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Relegation\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 43\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 ml-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"W\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-yellow-500 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"D\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex space-x-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-red-500 rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                                    lineNumber: 355,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"L\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 43\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 31\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 25\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n                lineNumber: 247,\n                columnNumber: 19\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/LeagueStandings.tsx\",\n        lineNumber: 178,\n        columnNumber: 13\n    }, undefined);\n};\n_s(LeagueStandings, \"fHpo8ZeX7i7S0VBqPzFfmFn7m9U=\", false, function() {\n    return [\n        _lib_hooks_leagues_useLeagueStandings__WEBPACK_IMPORTED_MODULE_6__.useLeagueStandings\n    ];\n});\n_c = LeagueStandings;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LeagueStandings);\nvar _c;\n$RefreshReg$(_c, \"LeagueStandings\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2xlYWd1ZXMvZGV0YWlsL0xlYWd1ZVN0YW5kaW5ncy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTBCO0FBQ3NEO0FBQ2hDO0FBQ0Y7QUFDTTtBQVc5QjtBQUNvRTtBQUNyQztBQVE5QyxNQUFNbUIsa0JBQWtEO1FBQUMsRUFDMURDLFFBQVEsRUFDUkMsTUFBTSxFQUNOQyxZQUFZLEVBQUUsRUFDbkI7UUFnQnNCQyxrQkFBQUEsYUFDS0EsbUJBQUFBLGNBSXlCQSxtQkFBQUEsY0FxTFZDOztJQXpNckMsTUFBTSxFQUNBRCxTQUFTLEVBQ1RFLE9BQU8sRUFDUEMsS0FBSyxFQUNMQyxnQkFBZ0IsRUFDaEJILEtBQUssRUFDVixHQUFHUCx5RkFBa0JBLENBQUM7UUFBRUc7UUFBVUM7SUFBTztJQUUxQyxnQkFBZ0I7SUFDaEJPLFFBQVFDLEdBQUcsQ0FBQyxrREFBd0M7UUFDOUNUO1FBQ0FDO1FBQ0FTLGdCQUFnQlAsVUFBVVEsTUFBTTtRQUNoQ047UUFDQUM7UUFDQU0sU0FBUyxHQUFFVCxjQUFBQSxTQUFTLENBQUMsRUFBRSxjQUFaQSxtQ0FBQUEsbUJBQUFBLFlBQWNVLElBQUksY0FBbEJWLHVDQUFBQSxpQkFBb0JXLElBQUk7UUFDbkNDLGdCQUFnQlosRUFBQUEsZUFBQUEsU0FBUyxDQUFDLEVBQUUsY0FBWkEsb0NBQUFBLG9CQUFBQSxhQUFjVSxJQUFJLGNBQWxCVix3Q0FBQUEsa0JBQW9CVyxJQUFJLE1BQUssb0JBQW9CLDBCQUEwQjtJQUNqRztJQUVBLDBDQUEwQztJQUMxQyxNQUFNQyxpQkFBaUJaLFVBQVVRLE1BQU0sR0FBRyxLQUFLUixFQUFBQSxlQUFBQSxTQUFTLENBQUMsRUFBRSxjQUFaQSxvQ0FBQUEsb0JBQUFBLGFBQWNVLElBQUksY0FBbEJWLHdDQUFBQSxrQkFBb0JXLElBQUksTUFBSztJQUM1RSxJQUFJQyxnQkFBZ0I7UUFDZFAsUUFBUVEsSUFBSSxDQUFDO0lBQ25CO0lBRUEsb0RBQW9EO0lBQ3BELE1BQU1DLFdBQVc7WUFBQyxFQUFFSixJQUFJLEVBQWtDO1FBQ3BELE1BQU1LLFVBQVVwQixrRUFBZ0JBLENBQUNlLEtBQUtNLElBQUk7UUFFMUMsSUFBSSxDQUFDRCxTQUFTO1lBQ1IscUJBQ00sOERBQUNFO2dCQUFJbEIsV0FBVTswQkFDUlcsS0FBS0MsSUFBSSxDQUFDTyxLQUFLLENBQUMsR0FBRyxHQUFHQyxXQUFXOzs7Ozs7UUFHcEQ7UUFFQSxxQkFDTSw4REFBQ0M7WUFDS0MsS0FBS047WUFDTE8sS0FBSyxHQUFhLE9BQVZaLEtBQUtDLElBQUksRUFBQztZQUNsQlosV0FBVTtZQUNWd0IsU0FBUyxDQUFDQztnQkFDSixNQUFNQyxTQUFTRCxFQUFFQyxNQUFNO2dCQUN2QkEsT0FBT0MsS0FBSyxDQUFDQyxPQUFPLEdBQUc7Z0JBQ3ZCLE1BQU1DLFdBQVdILE9BQU9JLGtCQUFrQjtnQkFDMUMsSUFBSUQsVUFBVUEsU0FBU0YsS0FBSyxDQUFDQyxPQUFPLEdBQUc7WUFDN0M7Ozs7OztJQUdsQjtJQUVBLDJCQUEyQjtJQUMzQixNQUFNRyxnQkFBZ0I7WUFBQyxFQUFFQyxJQUFJLEVBQWlDOzZCQUN4RCw4REFBQ2Q7WUFBSWxCLFdBQVU7c0JBQ1JnQyxLQUFLQyxHQUFHLENBQUMsQ0FBQ0MsUUFBUUMsc0JBQ2IsOERBQUNqQjtvQkFFS2xCLFdBQVcsd0JBS0osT0FMNEJrQyxXQUFXLE1BQ3RDLGlCQUNBQSxXQUFXLE1BQ0wsZUFDQTtvQkFFZEUsT0FBT0YsV0FBVyxNQUFNLFFBQVFBLFdBQVcsTUFBTSxTQUFTO21CQVByREM7Ozs7Ozs7Ozs7O0lBYTdCLDRCQUE0QjtJQUM1QixNQUFNRSxvQkFBb0I7WUFBQyxFQUFFQyxRQUFRLEVBQXdCO1FBQ3ZELElBQUlBLGFBQWEsR0FBRztZQUNkLHFCQUFPLDhEQUFDL0MsdUpBQUtBO2dCQUFDUyxXQUFVOzs7Ozs7UUFDOUI7UUFDQSxJQUFJc0MsWUFBWSxHQUFHO1lBQ2IscUJBQU8sOERBQUNuRCx1SkFBVUE7Z0JBQUNhLFdBQVU7Ozs7OztRQUNuQztRQUNBLElBQUlzQyxZQUFZckMsVUFBVVEsTUFBTSxHQUFHLEdBQUc7WUFDaEMscUJBQU8sOERBQUNyQix3SkFBWUE7Z0JBQUNZLFdBQVU7Ozs7OztRQUNyQztRQUNBLHFCQUFPLDhEQUFDWCx3SkFBS0E7WUFBQ1csV0FBVTs7Ozs7O0lBQzlCO0lBRUEsSUFBSUcsU0FBUztRQUNQLHFCQUNNLDhEQUFDeEIscURBQUlBO1lBQUNxQixXQUFXQTs7OEJBQ1gsOERBQUNuQiwyREFBVUE7OEJBQ0wsNEVBQUNDLDBEQUFTQTt3QkFBQ2tCLFdBQVU7OzBDQUNmLDhEQUFDZCx3SkFBTUE7Z0NBQUNjLFdBQVU7Ozs7OzswQ0FDbEIsOERBQUN1QzswQ0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBR2xCLDhEQUFDM0QsNERBQVdBO29CQUFDb0IsV0FBVTs4QkFDaEI7MkJBQUl3QyxNQUFNO3FCQUFJLENBQUNQLEdBQUcsQ0FBQyxDQUFDUSxHQUFHQyxrQkFDbEIsOERBQUN4Qjs0QkFBWWxCLFdBQVU7OzhDQUNqQiw4REFBQ2YsNkRBQVFBO29DQUFDZSxXQUFVOzs7Ozs7OENBQ3BCLDhEQUFDZiw2REFBUUE7b0NBQUNlLFdBQVU7Ozs7Ozs4Q0FDcEIsOERBQUNmLDZEQUFRQTtvQ0FBQ2UsV0FBVTs7Ozs7OzhDQUNwQiw4REFBQ2YsNkRBQVFBO29DQUFDZSxXQUFVOzs7Ozs7OENBQ3BCLDhEQUFDZiw2REFBUUE7b0NBQUNlLFdBQVU7Ozs7Ozs7MkJBTGhCMEM7Ozs7Ozs7Ozs7Ozs7Ozs7SUFXeEM7SUFFQSxJQUFJdEMsT0FBTztRQUNMLHFCQUNNLDhEQUFDekIscURBQUlBO1lBQUNxQixXQUFXQTs7OEJBQ1gsOERBQUNuQiwyREFBVUE7OEJBQ0wsNEVBQUNDLDBEQUFTQTt3QkFBQ2tCLFdBQVU7OzBDQUNmLDhEQUFDa0I7Z0NBQUlsQixXQUFVOztrREFDVCw4REFBQ2Qsd0pBQU1BO3dDQUFDYyxXQUFVOzs7Ozs7a0RBQ2xCLDhEQUFDdUM7a0RBQUs7Ozs7Ozs7Ozs7OzswQ0FFWiw4REFBQ3hELHlEQUFNQTtnQ0FDRDRELFNBQVE7Z0NBQ1JDLE1BQUs7Z0NBQ0xDLFNBQVN4QzswQ0FFVCw0RUFBQ2Ysd0pBQVNBO29DQUFDVSxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQUl2Qyw4REFBQ3BCLDREQUFXQTs4QkFDTiw0RUFBQ3NDO3dCQUFJbEIsV0FBVTs7MENBQ1QsOERBQUNOLHdKQUFTQTtnQ0FBQ00sV0FBVTs7Ozs7OzBDQUNyQiw4REFBQzhDOzBDQUFFOzs7Ozs7MENBQ0gsOERBQUMvRCx5REFBTUE7Z0NBQ0Q0RCxTQUFRO2dDQUNSQyxNQUFLO2dDQUNMQyxTQUFTeEM7Z0NBQ1RMLFdBQVU7MENBQ2Y7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBTy9CO0lBRUEscUJBQ00sOERBQUNyQixxREFBSUE7UUFBQ3FCLFdBQVdBOzswQkFDWCw4REFBQ25CLDJEQUFVQTs7a0NBQ0wsOERBQUNxQzt3QkFBSWxCLFdBQVU7OzBDQUNULDhEQUFDbEIsMERBQVNBO2dDQUFDa0IsV0FBVTs7a0RBQ2YsOERBQUNkLHdKQUFNQTt3Q0FBQ2MsV0FBVTs7Ozs7O2tEQUNsQiw4REFBQ3VDO2tEQUFLOzs7Ozs7a0RBQ04sOERBQUN2RCx1REFBS0E7d0NBQUMyRCxTQUFRO3dDQUFZM0MsV0FBVTtrREFDOUJELFVBQVUsSUFBSWdELE9BQU9DLFdBQVc7Ozs7Ozs7Ozs7OzswQ0FHN0MsOERBQUM5QjtnQ0FBSWxCLFdBQVU7O2tEQUNULDhEQUFDakIseURBQU1BO3dDQUNENEQsU0FBUTt3Q0FDUkMsTUFBSzt3Q0FDTEMsU0FBUzs0Q0FDSHZDLFFBQVFDLEdBQUcsQ0FBQzs0Q0FDWkY7d0NBQ047d0NBQ0FMLFdBQVU7OzBEQUVWLDhEQUFDVix3SkFBU0E7Z0RBQUNVLFdBQVU7Ozs7Ozs0Q0FBaUI7Ozs7Ozs7a0RBRzVDLDhEQUFDakIseURBQU1BO3dDQUNENEQsU0FBUTt3Q0FDUkMsTUFBSzt3Q0FDTEMsU0FBUzs0Q0FDSHZDLFFBQVFDLEdBQUcsQ0FBQzs0Q0FDWix3Q0FBd0M7NENBQ3hDMEMsYUFBYUMsVUFBVSxDQUFDLGFBQXlCbkQsT0FBWkQsVUFBUyxLQUFVLE9BQVBDOzRDQUNqRCxtQ0FBbUM7NENBQ25Db0QsT0FBT0MsUUFBUSxDQUFDQyxNQUFNO3dDQUM1Qjt3Q0FDQXJELFdBQVU7OzBEQUVWLDhEQUFDVix3SkFBU0E7Z0RBQUNVLFdBQVU7Ozs7Ozs0Q0FBaUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBT3hELDhEQUFDa0I7d0JBQUlsQixXQUFVOzswQ0FDVCw4REFBQ2tCO2dDQUFJbEIsV0FBVTs7a0RBQ1QsOERBQUNrQjt3Q0FBSWxCLFdBQVU7OzBEQUNULDhEQUFDUCx3SkFBS0E7Z0RBQUNPLFdBQVU7Ozs7OzswREFDakIsOERBQUN1QzswREFBSzs7Ozs7Ozs7Ozs7O2tEQUVaLDhEQUFDckI7d0NBQUlsQixXQUFVO2tEQUFpQkUsTUFBTW9ELFVBQVU7Ozs7Ozs7Ozs7OzswQ0FFdEQsOERBQUNwQztnQ0FBSWxCLFdBQVU7O2tEQUNULDhEQUFDa0I7d0NBQUlsQixXQUFVOzswREFDVCw4REFBQ1QsdUpBQUtBO2dEQUFDUyxXQUFVOzs7Ozs7MERBQ2pCLDhEQUFDdUM7MERBQUs7Ozs7Ozs7Ozs7OztrREFFWiw4REFBQ3JCO3dDQUFJbEIsV0FBVTtrREFDUkUsRUFBQUEsaUJBQUFBLE1BQU1xRCxPQUFPLGNBQWJyRCxxQ0FBQUEsZUFBZVMsSUFBSSxDQUFDQyxJQUFJLENBQUM0QyxTQUFTLENBQUMsR0FBRyxRQUFPOzs7Ozs7Ozs7Ozs7MENBRzFELDhEQUFDdEM7Z0NBQUlsQixXQUFVOztrREFDVCw4REFBQ2tCO3dDQUFJbEIsV0FBVTs7MERBQ1QsOERBQUNSLHdKQUFNQTtnREFBQ1EsV0FBVTs7Ozs7OzBEQUNsQiw4REFBQ3VDOzBEQUFLOzs7Ozs7Ozs7Ozs7a0RBRVosOERBQUNyQjt3Q0FBSWxCLFdBQVU7a0RBQWlCRSxNQUFNdUQsZUFBZTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQUt2RSw4REFBQzdFLDREQUFXQTs7a0NBRU4sOERBQUNzQzt3QkFBSWxCLFdBQVU7OzBDQUNULDhEQUFDa0I7Z0NBQUlsQixXQUFVOzBDQUF5Qjs7Ozs7OzBDQUN4Qyw4REFBQ2tCO2dDQUFJbEIsV0FBVTswQ0FBYTs7Ozs7OzBDQUM1Qiw4REFBQ2tCO2dDQUFJbEIsV0FBVTswQ0FBeUI7Ozs7OzswQ0FDeEMsOERBQUNrQjtnQ0FBSWxCLFdBQVU7MENBQXlCOzs7Ozs7MENBQ3hDLDhEQUFDa0I7Z0NBQUlsQixXQUFVOzBDQUF5Qjs7Ozs7OzBDQUN4Qyw4REFBQ2tCO2dDQUFJbEIsV0FBVTswQ0FBeUI7Ozs7OzswQ0FDeEMsOERBQUNrQjtnQ0FBSWxCLFdBQVU7MENBQXlCOzs7Ozs7MENBQ3hDLDhEQUFDa0I7Z0NBQUlsQixXQUFVOzBDQUF5Qjs7Ozs7OzBDQUN4Qyw4REFBQ2tCO2dDQUFJbEIsV0FBVTswQ0FBeUI7Ozs7Ozs7Ozs7OztrQ0FJOUMsOERBQUNrQjt3QkFBSWxCLFdBQVU7a0NBQ1JDLFVBQVVnQyxHQUFHLENBQUMsQ0FBQ3lCLFVBQVV2QixzQkFDcEIsOERBQUNqQjtnQ0FFS2xCLFdBQVcsMkZBS0osT0FMK0ZtQyxRQUFRLElBQ3RHLDRDQUNBQSxTQUFTbEMsVUFBVVEsTUFBTSxHQUFHLElBQ3RCLHdDQUNBOztrREFJZCw4REFBQ1M7d0NBQUlsQixXQUFVO2tEQUNULDRFQUFDa0I7NENBQUlsQixXQUFVOzs4REFDVCw4REFBQ3VDO29EQUFLdkMsV0FBVTs4REFBVzBELFNBQVNwQixRQUFROzs7Ozs7OERBQzVDLDhEQUFDRDtvREFBa0JDLFVBQVVvQixTQUFTcEIsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBSzFELDhEQUFDcEI7d0NBQUlsQixXQUFVOzswREFDVCw4REFBQ2U7Z0RBQVNKLE1BQU0rQyxTQUFTL0MsSUFBSTs7Ozs7OzBEQUM3Qiw4REFBQ087Z0RBQUlsQixXQUFVOzBEQUNULDRFQUFDOEM7b0RBQUU5QyxXQUFVOzhEQUNOMEQsU0FBUy9DLElBQUksQ0FBQ0MsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBTXJDLDhEQUFDTTt3Q0FBSWxCLFdBQVU7a0RBQ1IwRCxTQUFTQyxXQUFXOzs7Ozs7a0RBSTNCLDhEQUFDekM7d0NBQUlsQixXQUFVO2tEQUNSMEQsU0FBU0UsSUFBSTs7Ozs7O2tEQUlwQiw4REFBQzFDO3dDQUFJbEIsV0FBVTtrREFDUjBELFNBQVNHLEtBQUs7Ozs7OztrREFJckIsOERBQUMzQzt3Q0FBSWxCLFdBQVU7a0RBQ1IwRCxTQUFTSSxNQUFNOzs7Ozs7a0RBSXRCLDhEQUFDNUM7d0NBQUlsQixXQUFXLDhDQUtULE9BTHVEMEQsU0FBU0ssY0FBYyxHQUFHLElBQ2hGLG1CQUNBTCxTQUFTSyxjQUFjLEdBQUcsSUFDcEIsaUJBQ0E7OzRDQUVQTCxTQUFTSyxjQUFjLEdBQUcsSUFBSSxNQUFNOzRDQUFJTCxTQUFTSyxjQUFjOzs7Ozs7O2tEQUl0RSw4REFBQzdDO3dDQUFJbEIsV0FBVTtrREFDUjBELFNBQVNNLE1BQU07Ozs7OztrREFJdEIsOERBQUM5Qzt3Q0FBSWxCLFdBQVU7a0RBQ1QsNEVBQUMrQjs0Q0FBY0MsTUFBTTBCLFNBQVMxQixJQUFJOzs7Ozs7Ozs7Ozs7K0JBL0RuQzBCLFNBQVMvQyxJQUFJLENBQUNzRCxFQUFFOzs7Ozs7Ozs7O2tDQXNFdkMsOERBQUMvQzt3QkFBSWxCLFdBQVU7a0NBQ1QsNEVBQUNrQjs0QkFBSWxCLFdBQVU7OzhDQUNULDhEQUFDa0I7b0NBQUlsQixXQUFVOztzREFDVCw4REFBQ2tCOzRDQUFJbEIsV0FBVTs7Ozs7O3NEQUNmLDhEQUFDdUM7c0RBQUs7Ozs7Ozs7Ozs7Ozs4Q0FFWiw4REFBQ3JCO29DQUFJbEIsV0FBVTs7c0RBQ1QsOERBQUNrQjs0Q0FBSWxCLFdBQVU7Ozs7OztzREFDZiw4REFBQ3VDO3NEQUFLOzs7Ozs7Ozs7Ozs7OENBRVosOERBQUNyQjtvQ0FBSWxCLFdBQVU7O3NEQUNULDhEQUFDa0I7NENBQUlsQixXQUFVOzs4REFDVCw4REFBQ2tCO29EQUFJbEIsV0FBVTs7Ozs7OzhEQUNmLDhEQUFDdUM7OERBQUs7Ozs7Ozs7Ozs7OztzREFFWiw4REFBQ3JCOzRDQUFJbEIsV0FBVTs7OERBQ1QsOERBQUNrQjtvREFBSWxCLFdBQVU7Ozs7Ozs4REFDZiw4REFBQ3VDOzhEQUFLOzs7Ozs7Ozs7Ozs7c0RBRVosOERBQUNyQjs0Q0FBSWxCLFdBQVU7OzhEQUNULDhEQUFDa0I7b0RBQUlsQixXQUFVOzs7Ozs7OERBQ2YsOERBQUN1Qzs4REFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRdEQsRUFBRTtHQWhWVzFDOztRQVdIRixxRkFBa0JBOzs7S0FYZkU7QUFrVmIsK0RBQWVBLGVBQWVBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvbGVhZ3Vlcy9kZXRhaWwvTGVhZ3VlU3RhbmRpbmdzLnRzeD85MmQ2Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvY2FyZCc7XG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJztcbmltcG9ydCB7IEJhZGdlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2JhZGdlJztcbmltcG9ydCB7IFNrZWxldG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3NrZWxldG9uJztcbmltcG9ydCB7XG4gICAgICBUcm9waHksXG4gICAgICBUcmVuZGluZ1VwLFxuICAgICAgVHJlbmRpbmdEb3duLFxuICAgICAgTWludXMsXG4gICAgICBSZWZyZXNoQ3csXG4gICAgICBDcm93bixcbiAgICAgIFRhcmdldCxcbiAgICAgIFVzZXJzLFxuICAgICAgQmFyQ2hhcnQzLFxufSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IHsgdXNlTGVhZ3VlU3RhbmRpbmdzLCBTdGFuZGluZ0RhdGEgfSBmcm9tICdAL2xpYi9ob29rcy9sZWFndWVzL3VzZUxlYWd1ZVN0YW5kaW5ncyc7XG5pbXBvcnQgeyBidWlsZFRlYW1Mb2dvVXJsIH0gZnJvbSAnQC9saWIvdXRpbHMvaW1hZ2UnO1xuXG5pbnRlcmZhY2UgTGVhZ3VlU3RhbmRpbmdzUHJvcHMge1xuICAgICAgbGVhZ3VlSWQ6IHN0cmluZztcbiAgICAgIHNlYXNvbj86IG51bWJlcjtcbiAgICAgIGNsYXNzTmFtZT86IHN0cmluZztcbn1cblxuZXhwb3J0IGNvbnN0IExlYWd1ZVN0YW5kaW5nczogUmVhY3QuRkM8TGVhZ3VlU3RhbmRpbmdzUHJvcHM+ID0gKHtcbiAgICAgIGxlYWd1ZUlkLFxuICAgICAgc2Vhc29uLFxuICAgICAgY2xhc3NOYW1lID0gJycsXG59KSA9PiB7XG4gICAgICBjb25zdCB7XG4gICAgICAgICAgICBzdGFuZGluZ3MsXG4gICAgICAgICAgICBsb2FkaW5nLFxuICAgICAgICAgICAgZXJyb3IsXG4gICAgICAgICAgICByZWZyZXNoU3RhbmRpbmdzLFxuICAgICAgICAgICAgc3RhdHMsXG4gICAgICB9ID0gdXNlTGVhZ3VlU3RhbmRpbmdzKHsgbGVhZ3VlSWQsIHNlYXNvbiB9KTtcblxuICAgICAgLy8gRGVidWcgbG9nZ2luZ1xuICAgICAgY29uc29sZS5sb2coJ/CflI0gTGVhZ3VlU3RhbmRpbmdzIGNvbXBvbmVudCByZW5kZXI6Jywge1xuICAgICAgICAgICAgbGVhZ3VlSWQsXG4gICAgICAgICAgICBzZWFzb24sXG4gICAgICAgICAgICBzdGFuZGluZ3NDb3VudDogc3RhbmRpbmdzLmxlbmd0aCxcbiAgICAgICAgICAgIGxvYWRpbmcsXG4gICAgICAgICAgICBlcnJvcixcbiAgICAgICAgICAgIGZpcnN0VGVhbTogc3RhbmRpbmdzWzBdPy50ZWFtPy5uYW1lLFxuICAgICAgICAgICAgaXNGYWxsYmFja0RhdGE6IHN0YW5kaW5nc1swXT8udGVhbT8ubmFtZSA9PT0gJ01hbmNoZXN0ZXIgVW5pdGVkJyAvLyBDaGVjayBpZiB1c2luZyBmYWxsYmFja1xuICAgICAgfSk7XG5cbiAgICAgIC8vIENoZWNrIGlmIHdlJ3JlIGRpc3BsYXlpbmcgZmFsbGJhY2sgZGF0YVxuICAgICAgY29uc3QgaXNGYWxsYmFja0RhdGEgPSBzdGFuZGluZ3MubGVuZ3RoID4gMCAmJiBzdGFuZGluZ3NbMF0/LnRlYW0/Lm5hbWUgPT09ICdNYW5jaGVzdGVyIFVuaXRlZCc7XG4gICAgICBpZiAoaXNGYWxsYmFja0RhdGEpIHtcbiAgICAgICAgICAgIGNvbnNvbGUud2Fybign4pqg77iPIExlYWd1ZSBUYWJsZSBpcyBkaXNwbGF5aW5nIEZBTExCQUNLIGRhdGEgaW5zdGVhZCBvZiByZWFsIEFQSSBkYXRhIScpO1xuICAgICAgfVxuXG4gICAgICAvLyBUZWFtIExvZ28gQ29tcG9uZW50IHdpdGggQ0ROIHN1cHBvcnQgYW5kIGZhbGxiYWNrXG4gICAgICBjb25zdCBUZWFtTG9nbyA9ICh7IHRlYW0gfTogeyB0ZWFtOiBTdGFuZGluZ0RhdGFbJ3RlYW0nXSB9KSA9PiB7XG4gICAgICAgICAgICBjb25zdCBsb2dvVXJsID0gYnVpbGRUZWFtTG9nb1VybCh0ZWFtLmxvZ28pO1xuXG4gICAgICAgICAgICBpZiAoIWxvZ29VcmwpIHtcbiAgICAgICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1ibHVlLTUwMCB0by1wdXJwbGUtNjAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB0ZXh0LXdoaXRlIHRleHQteHMgZm9udC1ib2xkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7dGVhbS5uYW1lLnNsaWNlKDAsIDIpLnRvVXBwZXJDYXNlKCl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICAgICAgICBzcmM9e2xvZ29Vcmx9XG4gICAgICAgICAgICAgICAgICAgICAgICBhbHQ9e2Ake3RlYW0ubmFtZX0gbG9nb2B9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LTggaC04IG9iamVjdC1jb250YWluXCJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uRXJyb3I9eyhlKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCB0YXJnZXQgPSBlLnRhcmdldCBhcyBIVE1MSW1hZ2VFbGVtZW50O1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGFyZ2V0LnN0eWxlLmRpc3BsYXkgPSAnbm9uZSc7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBmYWxsYmFjayA9IHRhcmdldC5uZXh0RWxlbWVudFNpYmxpbmcgYXMgSFRNTEVsZW1lbnQ7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpZiAoZmFsbGJhY2spIGZhbGxiYWNrLnN0eWxlLmRpc3BsYXkgPSAnZmxleCc7XG4gICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICk7XG4gICAgICB9O1xuXG4gICAgICAvLyBGb3JtIGluZGljYXRvciBjb21wb25lbnRcbiAgICAgIGNvbnN0IEZvcm1JbmRpY2F0b3IgPSAoeyBmb3JtIH06IHsgZm9ybTogKCdXJyB8ICdMJyB8ICdEJylbXSB9KSA9PiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0xXCI+XG4gICAgICAgICAgICAgICAgICB7Zm9ybS5tYXAoKHJlc3VsdCwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2B3LTIgaC0yIHJvdW5kZWQtZnVsbCAke3Jlc3VsdCA9PT0gJ1cnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ncmVlbi01MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHJlc3VsdCA9PT0gJ0wnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1yZWQtNTAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnYmcteWVsbG93LTUwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdGl0bGU9e3Jlc3VsdCA9PT0gJ1cnID8gJ1dpbicgOiByZXN1bHQgPT09ICdMJyA/ICdMb3NzJyA6ICdEcmF3J31cbiAgICAgICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgKTtcblxuICAgICAgLy8gUG9zaXRpb24gY2hhbmdlIGluZGljYXRvclxuICAgICAgY29uc3QgUG9zaXRpb25JbmRpY2F0b3IgPSAoeyBwb3NpdGlvbiB9OiB7IHBvc2l0aW9uOiBudW1iZXIgfSkgPT4ge1xuICAgICAgICAgICAgaWYgKHBvc2l0aW9uID09PSAxKSB7XG4gICAgICAgICAgICAgICAgICByZXR1cm4gPENyb3duIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC15ZWxsb3ctNTAwXCIgLz47XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAocG9zaXRpb24gPD0gNCkge1xuICAgICAgICAgICAgICAgICAgcmV0dXJuIDxUcmVuZGluZ1VwIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ncmVlbi01MDBcIiAvPjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChwb3NpdGlvbiA+PSBzdGFuZGluZ3MubGVuZ3RoIC0gMikge1xuICAgICAgICAgICAgICAgICAgcmV0dXJuIDxUcmVuZGluZ0Rvd24gY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LXJlZC01MDBcIiAvPjtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiA8TWludXMgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWdyYXktNDAwXCIgLz47XG4gICAgICB9O1xuXG4gICAgICBpZiAobG9hZGluZykge1xuICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgIDxDYXJkIGNsYXNzTmFtZT17Y2xhc3NOYW1lfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJkSGVhZGVyPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUcm9waHkgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5MZWFndWUgVGFibGU8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJzcGFjZS15LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtbLi4uQXJyYXkoMTApXS5tYXAoKF8sIGkpID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpfSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTa2VsZXRvbiBjbGFzc05hbWU9XCJ3LTYgaC02XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTa2VsZXRvbiBjbGFzc05hbWU9XCJ3LTggaC04IHJvdW5kZWQtZnVsbFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2tlbGV0b24gY2xhc3NOYW1lPVwiZmxleC0xIGgtNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2tlbGV0b24gY2xhc3NOYW1lPVwidy04IGgtNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2tlbGV0b24gY2xhc3NOYW1lPVwidy0xMiBoLTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICApO1xuICAgICAgfVxuXG4gICAgICBpZiAoZXJyb3IpIHtcbiAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9e2NsYXNzTmFtZX0+XG4gICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRyb3BoeSBjbGFzc05hbWU9XCJoLTUgdy01XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPkxlYWd1ZSBUYWJsZTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXtyZWZyZXNoU3RhbmRpbmdzfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFJlZnJlc2hDdyBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJhckNoYXJ0MyBjbGFzc05hbWU9XCJoLTEyIHctMTIgbXgtYXV0byBtYi0yIG9wYWNpdHktNTBcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHA+VW5hYmxlIHRvIGxvYWQgc3RhbmRpbmdzPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e3JlZnJlc2hTdGFuZGluZ3N9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJtdC0yXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFRyeSBBZ2FpblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICA8L0NhcmQ+XG4gICAgICAgICAgICApO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gKFxuICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPXtjbGFzc05hbWV9PlxuICAgICAgICAgICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUcm9waHkgY2xhc3NOYW1lPVwiaC01IHctNVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5MZWFndWUgVGFibGU8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgdmFyaWFudD1cInNlY29uZGFyeVwiIGNsYXNzTmFtZT1cIm1sLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtzZWFzb24gfHwgbmV3IERhdGUoKS5nZXRGdWxsWWVhcigpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+UhCBNYW51YWwgcmVmcmVzaCB0cmlnZ2VyZWQnKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlZnJlc2hTdGFuZGluZ3MoKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXhzXCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxSZWZyZXNoQ3cgY2xhc3NOYW1lPVwiaC00IHctNCBtci0xXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFJlZnJlc2hcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImdob3N0XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+UhCBGb3JjZSBjbGVhciBjYWNoZSBhbmQgcmVmcmVzaCcpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gRm9yY2UgY2xlYXIgbG9jYWxTdG9yYWdlIGNhY2hlIGlmIGFueVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbG9jYWxTdG9yYWdlLnJlbW92ZUl0ZW0oYHN0YW5kaW5ncy0ke2xlYWd1ZUlkfS0ke3NlYXNvbn1gKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIEZvcmNlIHJlZnJlc2ggd2l0aCBuZXcgdGltZXN0YW1wXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB3aW5kb3cubG9jYXRpb24ucmVsb2FkKCk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXJlZC02MDBcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFJlZnJlc2hDdyBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTFcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgRm9yY2UgUmVmcmVzaFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICB7LyogU3RhdGlzdGljcyBTdW1tYXJ5ICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0zIGdhcC00IG10LTQgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBzcGFjZS14LTEgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VXNlcnMgY2xhc3NOYW1lPVwiaC0zIHctM1wiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5UZWFtczwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkXCI+e3N0YXRzLnRvdGFsVGVhbXN9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgc3BhY2UteC0xIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENyb3duIGNsYXNzTmFtZT1cImgtMyB3LTNcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+TGVhZGVyPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC14c1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3N0YXRzLnRvcFRlYW0/LnRlYW0ubmFtZS5zdWJzdHJpbmcoMCwgMTApIHx8ICdOL0EnfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgc3BhY2UteC0xIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRhcmdldCBjbGFzc05hbWU9XCJoLTMgdy0zXCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPkF2ZyBHb2Fsczwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkXCI+e3N0YXRzLmF2Z0dvYWxzUGVyR2FtZX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG5cbiAgICAgICAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBUYWJsZSBIZWFkZXIgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEyIGdhcC0yIHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kIG1iLTMgcHgtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc3Bhbi0xIHRleHQtY2VudGVyXCI+IzwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc3Bhbi00XCI+VGVhbTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc3Bhbi0xIHRleHQtY2VudGVyXCI+UDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc3Bhbi0xIHRleHQtY2VudGVyXCI+VzwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc3Bhbi0xIHRleHQtY2VudGVyXCI+RDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc3Bhbi0xIHRleHQtY2VudGVyXCI+TDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc3Bhbi0xIHRleHQtY2VudGVyXCI+R0Q8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29sLXNwYW4tMSB0ZXh0LWNlbnRlclwiPlB0czwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc3Bhbi0xIHRleHQtY2VudGVyXCI+Rm9ybTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBTdGFuZGluZ3MgTGlzdCAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c3RhbmRpbmdzLm1hcCgoc3RhbmRpbmcsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBrZXk9e3N0YW5kaW5nLnRlYW0uaWR9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BncmlkIGdyaWQtY29scy0xMiBnYXAtMiBpdGVtcy1jZW50ZXIgcC0yIHJvdW5kZWQtbGcgaG92ZXI6YmctbXV0ZWQvNTAgdHJhbnNpdGlvbi1jb2xvcnMgJHtpbmRleCA8IDRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyZWVuLTUwIGJvcmRlci1sLTQgYm9yZGVyLWdyZWVuLTUwMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDogaW5kZXggPj0gc3RhbmRpbmdzLmxlbmd0aCAtIDNcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLXJlZC01MCBib3JkZXItbC00IGJvcmRlci1yZWQtNTAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnYmctYmFja2dyb3VuZCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIFBvc2l0aW9uICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc3Bhbi0xIHRleHQtY2VudGVyIGZvbnQtbWVkaXVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbVwiPntzdGFuZGluZy5wb3NpdGlvbn08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8UG9zaXRpb25JbmRpY2F0b3IgcG9zaXRpb249e3N0YW5kaW5nLnBvc2l0aW9ufSAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgey8qIFRlYW0gKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1zcGFuLTQgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGVhbUxvZ28gdGVhbT17c3RhbmRpbmcudGVhbX0gLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLXctMCBmbGV4LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtc20gdHJ1bmNhdGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtzdGFuZGluZy50ZWFtLm5hbWV9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogUGxheWVkICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc3Bhbi0xIHRleHQtY2VudGVyIHRleHQtc21cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtzdGFuZGluZy5wbGF5ZWRHYW1lc31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogV2lucyAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29sLXNwYW4tMSB0ZXh0LWNlbnRlciB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JlZW4tNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c3RhbmRpbmcud2luc31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogRHJhd3MgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1zcGFuLTEgdGV4dC1jZW50ZXIgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LXllbGxvdy02MDBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtzdGFuZGluZy5kcmF3c31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogTG9zc2VzICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc3Bhbi0xIHRleHQtY2VudGVyIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1yZWQtNjAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c3RhbmRpbmcubG9zc2VzfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBHb2FsIERpZmZlcmVuY2UgKi99XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YGNvbC1zcGFuLTEgdGV4dC1jZW50ZXIgdGV4dC1zbSBmb250LW1lZGl1bSAke3N0YW5kaW5nLmdvYWxEaWZmZXJlbmNlID4gMFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAndGV4dC1ncmVlbi02MDAnXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IHN0YW5kaW5nLmdvYWxEaWZmZXJlbmNlIDwgMFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyAndGV4dC1yZWQtNjAwJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAndGV4dC1tdXRlZC1mb3JlZ3JvdW5kJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3N0YW5kaW5nLmdvYWxEaWZmZXJlbmNlID4gMCA/ICcrJyA6ICcnfXtzdGFuZGluZy5nb2FsRGlmZmVyZW5jZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7LyogUG9pbnRzICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc3Bhbi0xIHRleHQtY2VudGVyIHRleHQtc20gZm9udC1ib2xkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7c3RhbmRpbmcucG9pbnRzfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHsvKiBGb3JtICovfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc3Bhbi0xIGZsZXgganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxGb3JtSW5kaWNhdG9yIGZvcm09e3N0YW5kaW5nLmZvcm19IC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgey8qIExlZ2VuZCAqL31cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNCBwdC00IGJvcmRlci10XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC00IHRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTMgaC0xIGJnLWdyZWVuLTUwMCByb3VuZGVkXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5DaGFtcGlvbnMgTGVhZ3VlPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTMgaC0xIGJnLXJlZC01MDAgcm91bmRlZFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+UmVsZWdhdGlvbjwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTIgbWwtNFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLWdyZWVuLTUwMCByb3VuZGVkLWZ1bGxcIj48L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPlc8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTFcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy15ZWxsb3ctNTAwIHJvdW5kZWQtZnVsbFwiPjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+RDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLXJlZC01MDAgcm91bmRlZC1mdWxsXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5MPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IExlYWd1ZVN0YW5kaW5ncztcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJCdXR0b24iLCJCYWRnZSIsIlNrZWxldG9uIiwiVHJvcGh5IiwiVHJlbmRpbmdVcCIsIlRyZW5kaW5nRG93biIsIk1pbnVzIiwiUmVmcmVzaEN3IiwiQ3Jvd24iLCJUYXJnZXQiLCJVc2VycyIsIkJhckNoYXJ0MyIsInVzZUxlYWd1ZVN0YW5kaW5ncyIsImJ1aWxkVGVhbUxvZ29VcmwiLCJMZWFndWVTdGFuZGluZ3MiLCJsZWFndWVJZCIsInNlYXNvbiIsImNsYXNzTmFtZSIsInN0YW5kaW5ncyIsInN0YXRzIiwibG9hZGluZyIsImVycm9yIiwicmVmcmVzaFN0YW5kaW5ncyIsImNvbnNvbGUiLCJsb2ciLCJzdGFuZGluZ3NDb3VudCIsImxlbmd0aCIsImZpcnN0VGVhbSIsInRlYW0iLCJuYW1lIiwiaXNGYWxsYmFja0RhdGEiLCJ3YXJuIiwiVGVhbUxvZ28iLCJsb2dvVXJsIiwibG9nbyIsImRpdiIsInNsaWNlIiwidG9VcHBlckNhc2UiLCJpbWciLCJzcmMiLCJhbHQiLCJvbkVycm9yIiwiZSIsInRhcmdldCIsInN0eWxlIiwiZGlzcGxheSIsImZhbGxiYWNrIiwibmV4dEVsZW1lbnRTaWJsaW5nIiwiRm9ybUluZGljYXRvciIsImZvcm0iLCJtYXAiLCJyZXN1bHQiLCJpbmRleCIsInRpdGxlIiwiUG9zaXRpb25JbmRpY2F0b3IiLCJwb3NpdGlvbiIsInNwYW4iLCJBcnJheSIsIl8iLCJpIiwidmFyaWFudCIsInNpemUiLCJvbkNsaWNrIiwicCIsIkRhdGUiLCJnZXRGdWxsWWVhciIsImxvY2FsU3RvcmFnZSIsInJlbW92ZUl0ZW0iLCJ3aW5kb3ciLCJsb2NhdGlvbiIsInJlbG9hZCIsInRvdGFsVGVhbXMiLCJ0b3BUZWFtIiwic3Vic3RyaW5nIiwiYXZnR29hbHNQZXJHYW1lIiwic3RhbmRpbmciLCJwbGF5ZWRHYW1lcyIsIndpbnMiLCJkcmF3cyIsImxvc3NlcyIsImdvYWxEaWZmZXJlbmNlIiwicG9pbnRzIiwiaWQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/leagues/detail/LeagueStandings.tsx\n"));

/***/ })

});