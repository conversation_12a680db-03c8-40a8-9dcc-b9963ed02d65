"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/leagues/route";
exports.ids = ["app/api/leagues/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fleagues%2Froute&page=%2Fapi%2Fleagues%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fleagues%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fleagues%2Froute&page=%2Fapi%2Fleagues%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fleagues%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_duyanhstar_FECMS_sport_src_app_api_leagues_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/leagues/route.ts */ \"(rsc)/./src/app/api/leagues/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/leagues/route\",\n        pathname: \"/api/leagues\",\n        filename: \"route\",\n        bundlePath: \"app/api/leagues/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/FECMS-sport/src/app/api/leagues/route.ts\",\n    nextConfigOutput,\n    userland: _home_duyanhstar_FECMS_sport_src_app_api_leagues_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/leagues/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fleagues%2Froute&page=%2Fapi%2Fleagues%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fleagues%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/leagues/route.ts":
/*!**************************************!*\
  !*** ./src/app/api/leagues/route.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nconst dynamic = \"force-dynamic\";\nconst API_BASE_URL = \"http://localhost:3000\" || 0;\n// Helper function to get auth token from request\nfunction getAuthToken(request) {\n    const authHeader = request.headers.get(\"authorization\");\n    return authHeader?.startsWith(\"Bearer \") ? authHeader : null;\n}\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        // Forward all query parameters\n        const params = new URLSearchParams();\n        searchParams.forEach((value, key)=>{\n            params.append(key, value);\n        });\n        console.log(\"\\uD83D\\uDD04 Proxying leagues GET request:\", `${API_BASE_URL}/football/leagues?${params.toString()}`);\n        const response = await fetch(`${API_BASE_URL}/football/leagues?${params.toString()}`, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...request.headers.get(\"authorization\") && {\n                    \"Authorization\": request.headers.get(\"authorization\")\n                }\n            }\n        });\n        if (!response.ok) {\n            console.error(\"❌ API Error:\", response.status, response.statusText);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to fetch leagues\",\n                status: response.status,\n                message: response.statusText\n            }, {\n                status: response.status\n            });\n        }\n        const data = await response.json();\n        console.log(\"✅ Leagues fetched successfully:\", data.meta);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        console.error(\"❌ Proxy Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\",\n            message: error.message\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const authToken = getAuthToken(request);\n        if (!authToken) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Authentication required\"\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        console.log(\"\\uD83D\\uDD04 Proxying leagues POST request:\", `${API_BASE_URL}/football/leagues`);\n        console.log(\"\\uD83D\\uDCDD Request body:\", body);\n        const response = await fetch(`${API_BASE_URL}/football/leagues`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": authToken\n            },\n            body: JSON.stringify(body)\n        });\n        console.log(\"\\uD83D\\uDCE1 Real API response status:\", response.status);\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({\n                    message: response.statusText\n                }));\n            console.error(\"❌ API Error:\", response.status, errorData);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to create league\",\n                status: response.status,\n                message: errorData.message || response.statusText,\n                details: errorData\n            }, {\n                status: response.status\n            });\n        }\n        const data = await response.json();\n        console.log(\"✅ League created successfully:\", data);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        console.error(\"❌ Proxy Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\",\n            message: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/leagues/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fleagues%2Froute&page=%2Fapi%2Fleagues%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fleagues%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();