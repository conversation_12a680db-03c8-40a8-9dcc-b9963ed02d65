"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/leagues/[id]/page",{

/***/ "(app-pages-browser)/./src/components/leagues/detail/TeamQuickViewModal.tsx":
/*!**************************************************************!*\
  !*** ./src/components/leagues/detail/TeamQuickViewModal.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TeamQuickViewModal: function() { return /* binding */ TeamQuickViewModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3Icon,CalendarIcon,ExternalLinkIcon,MapPinIcon,TrophyIcon,UsersIcon,XIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3Icon,CalendarIcon,ExternalLinkIcon,MapPinIcon,TrophyIcon,UsersIcon,XIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3Icon,CalendarIcon,ExternalLinkIcon,MapPinIcon,TrophyIcon,UsersIcon,XIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3Icon,CalendarIcon,ExternalLinkIcon,MapPinIcon,TrophyIcon,UsersIcon,XIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3Icon,CalendarIcon,ExternalLinkIcon,MapPinIcon,TrophyIcon,UsersIcon,XIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3Icon,CalendarIcon,ExternalLinkIcon,MapPinIcon,TrophyIcon,UsersIcon,XIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3Icon,CalendarIcon,ExternalLinkIcon,MapPinIcon,TrophyIcon,UsersIcon,XIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _lib_utils_image__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils/image */ \"(app-pages-browser)/./src/lib/utils/image.ts\");\n/* __next_internal_client_entry_do_not_use__ TeamQuickViewModal,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst TeamQuickViewModal = (param)=>{\n    let { team, leagueId, season, isOpen, onClose } = param;\n    var _fixtures_played, _fixtures_wins, _fixtures_draws, _fixtures_loses, _goals_for_total, _goals_for, _goals_against_total, _goals_against;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [teamStats, setTeamStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch team statistics when modal opens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!team || !isOpen) return;\n        const fetchTeamStats = async ()=>{\n            setLoading(true);\n            setError(null);\n            try {\n                console.log(\"\\uD83D\\uDD04 Fetching team statistics:\", {\n                    teamId: team.externalId,\n                    leagueId,\n                    season: season || new Date().getFullYear()\n                });\n                const response = await fetch(\"/api/teams/statistics?team=\".concat(team.externalId, \"&league=\").concat(leagueId, \"&season=\").concat(season || new Date().getFullYear()));\n                if (!response.ok) {\n                    throw new Error(\"Failed to fetch team statistics: \".concat(response.status));\n                }\n                const data = await response.json();\n                console.log(\"✅ Team statistics response:\", data);\n                setTeamStats(data.data);\n            } catch (err) {\n                console.error(\"❌ Error fetching team statistics:\", err);\n                setError(err instanceof Error ? err.message : \"Failed to load team statistics\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchTeamStats();\n    }, [\n        team,\n        leagueId,\n        season,\n        isOpen\n    ]);\n    if (!team) return null;\n    // Calculate stats from API data or fallback to basic data\n    const stats = (teamStats === null || teamStats === void 0 ? void 0 : teamStats.statistics) || {};\n    const fixtures = stats.fixtures || {};\n    const goals = stats.goals || {};\n    const matchesPlayed = ((_fixtures_played = fixtures.played) === null || _fixtures_played === void 0 ? void 0 : _fixtures_played.total) || 0;\n    const wins = ((_fixtures_wins = fixtures.wins) === null || _fixtures_wins === void 0 ? void 0 : _fixtures_wins.total) || 0;\n    const draws = ((_fixtures_draws = fixtures.draws) === null || _fixtures_draws === void 0 ? void 0 : _fixtures_draws.total) || 0;\n    const losses = ((_fixtures_loses = fixtures.loses) === null || _fixtures_loses === void 0 ? void 0 : _fixtures_loses.total) || 0;\n    const goalsFor = ((_goals_for = goals.for) === null || _goals_for === void 0 ? void 0 : (_goals_for_total = _goals_for.total) === null || _goals_for_total === void 0 ? void 0 : _goals_for_total.total) || 0;\n    const goalsAgainst = ((_goals_against = goals.against) === null || _goals_against === void 0 ? void 0 : (_goals_against_total = _goals_against.total) === null || _goals_against_total === void 0 ? void 0 : _goals_against_total.total) || 0;\n    const points = wins * 3 + draws;\n    const winRate = matchesPlayed > 0 ? Math.round(wins / matchesPlayed * 100) : 0;\n    const goalDifference = goalsFor - goalsAgainst;\n    // Handle navigation to team statistics page\n    const handleViewDetails = ()=>{\n        router.push(\"/dashboard/teams/\".concat(team.externalId, \"/statistics\"));\n        onClose();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n        open: isOpen,\n        onOpenChange: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n            className: \"max-w-2xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                    className: \"text-xl font-semibold\",\n                                    children: \"Team Quick View\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    onClick: onClose,\n                                    className: \"h-8 w-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 43\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 31\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.Avatar, {\n                                    className: \"h-16 w-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.AvatarImage, {\n                                            src: (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_8__.buildTeamLogoUrl)(team.logo),\n                                            alt: \"\".concat(team.name, \" logo\")\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.AvatarFallback, {\n                                            className: \"bg-blue-500 text-white text-lg font-bold\",\n                                            children: team.name.substring(0, 2).toUpperCase()\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 43\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: team.name\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 text-sm text-muted-foreground mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                            lineNumber: 145,\n                                                            columnNumber: 55\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"Founded \",\n                                                                team.founded || \"N/A\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                            lineNumber: 146,\n                                                            columnNumber: 55\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 55\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: team.country || \"Unknown\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 55\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 43\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: \"secondary\",\n                                    className: \"text-lg px-3 py-1\",\n                                    children: [\n                                        \"ID: \",\n                                        team.externalId\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 31\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 25\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        team.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-muted-foreground leading-relaxed\",\n                                children: team.description\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 43\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 37\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 31\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-3 bg-muted/50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-blue-600\",\n                                            children: team.stats.points\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Points\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 43\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 174,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-3 bg-muted/50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: [\n                                                winRate,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Win Rate\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 43\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-3 bg-muted/50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold \".concat(goalDifference >= 0 ? \"text-green-600\" : \"text-red-600\"),\n                                            children: [\n                                                goalDifference >= 0 ? \"+\" : \"\",\n                                                goalDifference\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Goal Diff\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 43\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center p-3 bg-muted/50 rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: team.stats.matchesPlayed\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"Matches\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 43\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 31\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold mb-3 flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Recent Form (Last 5 matches)\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 43\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        team.recentForm.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold \".concat(getFormColor(result)),\n                                                children: result\n                                            }, index, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 49\n                                            }, undefined)),\n                                        team.recentForm.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"No recent matches\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 49\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 31\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold mb-3\",\n                                    children: \"Season Statistics\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-4 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"Wins:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-green-600\",\n                                                    children: team.stats.wins\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 225,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"Draws:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-yellow-600\",\n                                                    children: team.stats.draws\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"Losses:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-red-600\",\n                                                    children: team.stats.losses\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 235,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"Goals For:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: team.stats.goalsFor\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"Goals Against:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 242,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: team.stats.goalsAgainst\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"Total Points:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-blue-600\",\n                                                    children: team.stats.points\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 247,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 43\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 222,\n                            columnNumber: 31\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 252,\n                            columnNumber: 31\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                            children: [\n                                team.stadium && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-muted-foreground\",\n                                            children: \"Stadium:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: team.stadium\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        team.capacity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-muted-foreground\",\n                                            children: [\n                                                \"(\",\n                                                team.capacity.toLocaleString(),\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 55\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 43\n                                }, undefined),\n                                team.manager && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 268,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-muted-foreground\",\n                                            children: \"Manager:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 269,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: team.manager\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 49\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 267,\n                                    columnNumber: 43\n                                }, undefined),\n                                team.playersCount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-muted-foreground\",\n                                            children: \"Squad Size:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 276,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: [\n                                                team.playersCount,\n                                                \" players\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 277,\n                                            columnNumber: 49\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 43\n                                }, undefined),\n                                team.website && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: team.website,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"text-blue-600 hover:underline font-medium\",\n                                            children: \"Official Website\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 49\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 43\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 31\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 25\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogFooter, {\n                    className: \"space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"outline\",\n                            onClick: onClose,\n                            children: \"Close\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 31\n                        }, undefined),\n                        onViewDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>onViewDetails(team.id),\n                            children: \"View Full Details\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 37\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                    lineNumber: 296,\n                    columnNumber: 25\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n            lineNumber: 114,\n            columnNumber: 19\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n        lineNumber: 113,\n        columnNumber: 13\n    }, undefined);\n};\n_s(TeamQuickViewModal, \"oWpYBsScgldL8JmbHcF6f80CA18=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = TeamQuickViewModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TeamQuickViewModal);\nvar _c;\n$RefreshReg$(_c, \"TeamQuickViewModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/leagues/detail/TeamQuickViewModal.tsx\n"));

/***/ })

});