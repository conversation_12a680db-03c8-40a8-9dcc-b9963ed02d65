"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/teams/statistics/route";
exports.ids = ["app/api/teams/statistics/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fteams%2Fstatistics%2Froute&page=%2Fapi%2Fteams%2Fstatistics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fteams%2Fstatistics%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fteams%2Fstatistics%2Froute&page=%2Fapi%2Fteams%2Fstatistics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fteams%2Fstatistics%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_duyanhstar_FECMS_sport_src_app_api_teams_statistics_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/teams/statistics/route.ts */ \"(rsc)/./src/app/api/teams/statistics/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/teams/statistics/route\",\n        pathname: \"/api/teams/statistics\",\n        filename: \"route\",\n        bundlePath: \"app/api/teams/statistics/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/FECMS-sport/src/app/api/teams/statistics/route.ts\",\n    nextConfigOutput,\n    userland: _home_duyanhstar_FECMS_sport_src_app_api_teams_statistics_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/teams/statistics/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fteams%2Fstatistics%2Froute&page=%2Fapi%2Fteams%2Fstatistics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fteams%2Fstatistics%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/teams/statistics/route.ts":
/*!***********************************************!*\
  !*** ./src/app/api/teams/statistics/route.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nconst dynamic = \"force-dynamic\";\nconst API_BASE_URL = \"http://localhost:3000\" || 0;\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        // Required parameters\n        const league = searchParams.get(\"league\");\n        const season = searchParams.get(\"season\");\n        const team = searchParams.get(\"team\");\n        if (!league || !season || !team) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Missing required parameters\",\n                message: \"league, season, and team parameters are required\"\n            }, {\n                status: 400\n            });\n        }\n        // Forward all query parameters\n        const queryParams = new URLSearchParams();\n        searchParams.forEach((value, key)=>{\n            queryParams.append(key, value);\n        });\n        const queryString = queryParams.toString();\n        const url = `${API_BASE_URL}/football/teams/statistics?${queryString}`;\n        console.log(\"\\uD83D\\uDD04 Proxying team statistics request:\", url);\n        const response = await fetch(url, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) {\n            console.error(\"❌ Team Statistics API Error:\", response.status, response.statusText);\n            // Try to get error details\n            let errorMessage = response.statusText;\n            try {\n                const errorData = await response.json();\n                errorMessage = errorData.message || errorData.error || errorMessage;\n            } catch (e) {\n            // If response is not JSON, use status text\n            }\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to fetch team statistics\",\n                status: response.status,\n                message: errorMessage\n            }, {\n                status: response.status\n            });\n        }\n        const data = await response.json();\n        console.log(\"✅ Team statistics fetched successfully for team:\", team, \"league:\", league, \"season:\", season);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        console.error(\"❌ Team Statistics Proxy Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\",\n            message: error.message || \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/teams/statistics/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fteams%2Fstatistics%2Froute&page=%2Fapi%2Fteams%2Fstatistics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fteams%2Fstatistics%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();