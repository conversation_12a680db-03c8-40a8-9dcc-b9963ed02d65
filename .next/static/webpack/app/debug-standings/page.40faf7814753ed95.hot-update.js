"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/debug-standings/page",{

/***/ "(app-pages-browser)/./src/app/debug-standings/page.tsx":
/*!******************************************!*\
  !*** ./src/app/debug-standings/page.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DebugStandingsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction DebugStandingsPage() {\n    var _apiResponse_data, _apiResponse_data_, _apiResponse_data1, _apiResponse_data2;\n    _s();\n    const [apiResponse, setApiResponse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const testAPI = async ()=>{\n        setLoading(true);\n        setError(null);\n        setApiResponse(null);\n        try {\n            var _data_response__league_standings, _data_response__league, _data_response_, _data_response, _data_response1, _standingsArray__team, _standingsArray_, _standingsArray_1;\n            console.log(\"\\uD83D\\uDD04 Testing API call...\");\n            const apiUrl = \"/api/standings?league=340&season=2025&format=external&_t=\".concat(Date.now());\n            console.log(\"\\uD83C\\uDF10 API URL:\", apiUrl);\n            const response = await fetch(apiUrl, {\n                method: \"GET\",\n                headers: {\n                    \"Cache-Control\": \"no-cache, no-store, must-revalidate\",\n                    \"Pragma\": \"no-cache\",\n                    \"Expires\": \"0\"\n                }\n            });\n            console.log(\"\\uD83D\\uDCE1 Response status:\", response.status, response.statusText);\n            console.log(\"\\uD83D\\uDCE1 Response headers:\", Object.fromEntries(response.headers.entries()));\n            if (!response.ok) {\n                throw new Error(\"API Error: \".concat(response.status, \" \").concat(response.statusText));\n            }\n            const data = await response.json();\n            console.log(\"✅ API Response:\", data);\n            setApiResponse(data);\n            // Validate data structure - check both old and new format\n            const standingsArray = ((_data_response = data.response) === null || _data_response === void 0 ? void 0 : (_data_response_ = _data_response[0]) === null || _data_response_ === void 0 ? void 0 : (_data_response__league = _data_response_.league) === null || _data_response__league === void 0 ? void 0 : (_data_response__league_standings = _data_response__league.standings) === null || _data_response__league_standings === void 0 ? void 0 : _data_response__league_standings[0]) || data.data || [];\n            console.log(\"\\uD83D\\uDD0D Data validation:\", {\n                hasResponse: !!data.response,\n                hasData: !!data.data,\n                responseLength: (_data_response1 = data.response) === null || _data_response1 === void 0 ? void 0 : _data_response1.length,\n                standingsLength: standingsArray.length,\n                firstItem: standingsArray[0],\n                firstTeamName: ((_standingsArray_ = standingsArray[0]) === null || _standingsArray_ === void 0 ? void 0 : (_standingsArray__team = _standingsArray_.team) === null || _standingsArray__team === void 0 ? void 0 : _standingsArray__team.name) || ((_standingsArray_1 = standingsArray[0]) === null || _standingsArray_1 === void 0 ? void 0 : _standingsArray_1.teamName)\n            });\n        } catch (err) {\n            console.error(\"❌ API Test Error:\", err);\n            setError(err instanceof Error ? err.message : \"Unknown error\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        testAPI();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-8 space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-2xl font-bold\",\n                children: \"Debug Standings API\"\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            children: \"API Test Controls\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: testAPI,\n                            disabled: loading,\n                            children: loading ? \"Testing...\" : \"Test API Call\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"border-red-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"text-red-600\",\n                            children: \"Error\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-red-600\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                lineNumber: 83,\n                columnNumber: 9\n            }, this),\n            apiResponse && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            children: \"API Response Analysis\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-4 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Has Data:\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" \",\n                                            apiResponse.data ? \"✅ Yes\" : \"❌ No\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Is Array:\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" \",\n                                            Array.isArray(apiResponse.data) ? \"✅ Yes\" : \"❌ No\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"Data Length:\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" \",\n                                            ((_apiResponse_data = apiResponse.data) === null || _apiResponse_data === void 0 ? void 0 : _apiResponse_data.length) || 0\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"First Team:\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" \",\n                                            ((_apiResponse_data1 = apiResponse.data) === null || _apiResponse_data1 === void 0 ? void 0 : (_apiResponse_data_ = _apiResponse_data1[0]) === null || _apiResponse_data_ === void 0 ? void 0 : _apiResponse_data_.teamName) || \"N/A\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"First 3 Teams:\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                        className: \"list-disc list-inside mt-2\",\n                                        children: (_apiResponse_data2 = apiResponse.data) === null || _apiResponse_data2 === void 0 ? void 0 : _apiResponse_data2.slice(0, 3).map((team, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                children: [\n                                                    \"#\",\n                                                    team.position,\n                                                    \" \",\n                                                    team.teamName,\n                                                    \" - \",\n                                                    team.points,\n                                                    \" pts\"\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                        className: \"cursor-pointer font-medium\",\n                                        children: \"Raw Response\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"text-xs bg-gray-100 p-4 rounded mt-2 overflow-auto\",\n                                        children: JSON.stringify(apiResponse, null, 2)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                lineNumber: 94,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            children: \"Expected vs Actual\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-green-600\",\n                                            children: \"Expected (Correct)\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"#1 Nam Dinh - 54 pts\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"#2 Ha Noi - 46 pts\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"#3 C\\xf4ng An Nh\\xe2n D\\xe2n - 42 pts\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Total: 14 teams\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                                    lineNumber: 147,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-red-600\",\n                                            children: \"Fallback (Wrong)\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"text-sm space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"#1 Manchester United\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"#2 Manchester City\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"#3 Arsenal\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: \"Total: 10 teams\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/debug-standings/page.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n_s(DebugStandingsPage, \"ggDnxDEcNBnUAtqiKSUXcXzXizs=\");\n_c = DebugStandingsPage;\nvar _c;\n$RefreshReg$(_c, \"DebugStandingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/debug-standings/page.tsx\n"));

/***/ })

});