"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/leagues/[id]/page",{

/***/ "(app-pages-browser)/./src/lib/hooks/leagues/useLeagueStatistics.ts":
/*!******************************************************!*\
  !*** ./src/lib/hooks/leagues/useLeagueStatistics.ts ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLeagueStatistics: function() { return /* binding */ useLeagueStatistics; }\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useQuery.mjs\");\n\nfunction useLeagueStatistics(leagueExternalId, season) {\n    return (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_0__.useQuery)({\n        queryKey: [\n            \"league-statistics\",\n            leagueExternalId,\n            season\n        ],\n        queryFn: async ()=>{\n            console.log(\"\\uD83D\\uDD04 Fetching league statistics for:\", leagueExternalId, season);\n            try {\n                var _standingsData_response__league_standings, _standingsData_response__league, _standingsData_response_, _standingsData_response;\n                // Fetch real data from multiple sources\n                const currentSeason = season || new Date().getFullYear();\n                // 1. Get standings data for team statistics\n                const standingsResponse = await fetch(\"/api/standings?league=\".concat(leagueExternalId, \"&season=\").concat(currentSeason, \"&format=external&_t=\").concat(Date.now()));\n                const standingsData = await standingsResponse.json();\n                // 2. Get fixtures data for match statistics\n                const fixturesResponse = await fetch(\"/api/fixtures?league=\".concat(leagueExternalId, \"&season=\").concat(currentSeason, \"&limit=500&_t=\").concat(Date.now()));\n                const fixturesData = await fixturesResponse.json();\n                // Extract standings array\n                const standings = ((_standingsData_response = standingsData.response) === null || _standingsData_response === void 0 ? void 0 : (_standingsData_response_ = _standingsData_response[0]) === null || _standingsData_response_ === void 0 ? void 0 : (_standingsData_response__league = _standingsData_response_.league) === null || _standingsData_response__league === void 0 ? void 0 : (_standingsData_response__league_standings = _standingsData_response__league.standings) === null || _standingsData_response__league_standings === void 0 ? void 0 : _standingsData_response__league_standings[0]) || [];\n                // Extract fixtures array\n                const fixtures = fixturesData.data || [];\n                console.log(\"\\uD83D\\uDCCA Processing statistics from:\", {\n                    standingsCount: standings.length,\n                    fixturesCount: fixtures.length\n                });\n                // Calculate real statistics\n                const totalTeams = standings.length;\n                const totalFixtures = fixtures.length;\n                // Count fixtures by status\n                const completedFixtures = fixtures.filter((f)=>f.status === \"FT\" || f.status === \"AET\" || f.status === \"PEN\").length;\n                const upcomingFixtures = fixtures.filter((f)=>f.status === \"NS\" || f.status === \"TBD\").length;\n                const liveFixtures = fixtures.filter((f)=>f.status === \"1H\" || f.status === \"2H\" || f.status === \"HT\" || f.status === \"LIVE\").length;\n                // Calculate season progress\n                const seasonProgress = totalFixtures > 0 ? Math.round(completedFixtures / totalFixtures * 100) : 0;\n                // Calculate goals statistics\n                const totalGoals = standings.reduce((sum, team)=>{\n                    var _team_all_goals, _team_all;\n                    return sum + (((_team_all = team.all) === null || _team_all === void 0 ? void 0 : (_team_all_goals = _team_all.goals) === null || _team_all_goals === void 0 ? void 0 : _team_all_goals.for) || 0);\n                }, 0);\n                const totalGames = standings.reduce((sum, team)=>{\n                    var _team_all;\n                    return sum + (((_team_all = team.all) === null || _team_all === void 0 ? void 0 : _team_all.played) || 0);\n                }, 0);\n                const avgGoalsPerGame = totalGames > 0 ? Math.round(totalGoals / totalGames * 100) / 100 : 0;\n                // Find top scorer and best defense\n                const topScorer = standings.reduce((best, team)=>{\n                    var _team_all_goals, _team_all, _team_team;\n                    const goals = ((_team_all = team.all) === null || _team_all === void 0 ? void 0 : (_team_all_goals = _team_all.goals) === null || _team_all_goals === void 0 ? void 0 : _team_all_goals.for) || 0;\n                    return goals > (best.goals || 0) ? {\n                        team: (_team_team = team.team) === null || _team_team === void 0 ? void 0 : _team_team.name,\n                        goals\n                    } : best;\n                }, {\n                    team: \"\",\n                    goals: 0\n                });\n                const bestDefense = standings.reduce((best, team)=>{\n                    var _team_all_goals, _team_all, _team_team;\n                    const goalsAgainst = ((_team_all = team.all) === null || _team_all === void 0 ? void 0 : (_team_all_goals = _team_all.goals) === null || _team_all_goals === void 0 ? void 0 : _team_all_goals.against) || 999;\n                    return goalsAgainst < (best.goalsAgainst || 999) ? {\n                        team: (_team_team = team.team) === null || _team_team === void 0 ? void 0 : _team_team.name,\n                        goalsAgainst\n                    } : best;\n                }, {\n                    team: \"\",\n                    goalsAgainst: 999\n                });\n                // Generate insights based on real data\n                const insights = [];\n                if (seasonProgress > 0) {\n                    insights.push(\"Season is \".concat(seasonProgress, \"% complete with \").concat(completedFixtures, \" matches played\"));\n                }\n                if (avgGoalsPerGame > 0) {\n                    insights.push(\"Average \".concat(avgGoalsPerGame, \" goals per game this season\"));\n                }\n                if (topScorer.team) {\n                    insights.push(\"\".concat(topScorer.team, \" leads in goals scored with \").concat(topScorer.goals, \" goals\"));\n                }\n                if (bestDefense.team) {\n                    insights.push(\"\".concat(bestDefense.team, \" has the best defense with only \").concat(bestDefense.goalsAgainst, \" goals conceded\"));\n                }\n                return {\n                    totalTeams,\n                    totalFixtures,\n                    completedFixtures,\n                    upcomingFixtures,\n                    liveFixtures,\n                    seasonProgress,\n                    currentRound: \"Round \".concat(Math.ceil(completedFixtures / totalTeams) || 1),\n                    coverageScore: 10,\n                    insights,\n                    lastUpdated: new Date().toISOString(),\n                    totalGoals,\n                    avgGoalsPerGame,\n                    topScorer,\n                    bestDefense\n                };\n            } catch (error) {\n                console.error(\"❌ Error fetching league statistics:\", error);\n                // Fallback to basic mock data if API fails\n                return {\n                    totalTeams: 14,\n                    totalFixtures: 182,\n                    completedFixtures: 91,\n                    upcomingFixtures: 91,\n                    liveFixtures: 0,\n                    seasonProgress: 50,\n                    currentRound: \"Round 13\",\n                    coverageScore: 8,\n                    insights: [\n                        \"Statistics calculated from available data\",\n                        \"Real-time updates when API data is available\"\n                    ],\n                    lastUpdated: new Date().toISOString(),\n                    totalGoals: 0,\n                    avgGoalsPerGame: 0,\n                    topScorer: {\n                        team: \"\",\n                        goals: 0\n                    },\n                    bestDefense: {\n                        team: \"\",\n                        goalsAgainst: 0\n                    }\n                };\n            }\n        },\n        staleTime: 5 * 60 * 1000,\n        enabled: !!leagueExternalId\n    });\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/hooks/leagues/useLeagueStatistics.ts\n"));

/***/ })

});