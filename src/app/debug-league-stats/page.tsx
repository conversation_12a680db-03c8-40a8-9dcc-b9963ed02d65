'use client';

import { useState } from 'react';
import LeagueStatistics from '@/components/leagues/detail/LeagueStatistics';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

export default function DebugLeagueStatsPage() {
  const [leagueId, setLeagueId] = useState(340);
  const [season, setSeason] = useState(2025);

  // Mock league object
  const mockLeague = {
    id: 1,
    externalId: leagueId,
    name: 'V.League 1',
    country: 'Vietnam',
    logo: '',
    season: season,
    season_detail: {
      coverage: {
        standings: true,
        fixtures: true,
        players: true,
        top_scorers: true,
        predictions: false,
        odds: false
      }
    }
  };

  return (
    <div className="p-8 space-y-8">
      <h1 className="text-2xl font-bold">League Statistics Debug Page</h1>
      
      <Card>
        <CardHeader>
          <CardTitle>Test Controls</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">League ID</label>
              <Input
                type="number"
                value={leagueId}
                onChange={(e) => setLeagueId(parseInt(e.target.value))}
                placeholder="Enter league ID"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Season</label>
              <Input
                type="number"
                value={season}
                onChange={(e) => setSeason(parseInt(e.target.value))}
                placeholder="Enter season"
              />
            </div>
          </div>
          <Button onClick={() => window.location.reload()}>
            Refresh Component
          </Button>
        </CardContent>
      </Card>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">League Statistics Component</h2>
        <LeagueStatistics 
          key={`${leagueId}-${season}-${Date.now()}`}
          league={mockLeague}
          season={season}
        />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Expected Data Sources</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <p><strong>Standings API:</strong> /api/standings?league={leagueId}&season={season}</p>
            <p><strong>Fixtures API:</strong> /api/fixtures?league={leagueId}&season={season}</p>
            <p><strong>Expected Stats:</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>Total Teams: 14 (V.League 1)</li>
              <li>Total Goals: Calculated from standings</li>
              <li>Top Scorer: Nam Dinh (50 goals)</li>
              <li>Best Defense: Nam Dinh (18 goals conceded)</li>
              <li>Season Progress: Based on completed fixtures</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
