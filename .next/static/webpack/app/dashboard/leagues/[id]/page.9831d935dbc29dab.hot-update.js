"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/leagues/[id]/page",{

/***/ "(app-pages-browser)/./src/lib/hooks/leagues/useLeagueTeams.ts":
/*!*************************************************!*\
  !*** ./src/lib/hooks/leagues/useLeagueTeams.ts ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLeagueTeams: function() { return /* binding */ useLeagueTeams; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useLeagueTeams,default auto */ \n// Fetch teams from API\nconst fetchTeamsFromAPI = async (leagueId, season)=>{\n    try {\n        const currentSeason = season || new Date().getFullYear();\n        console.log(\"\\uD83D\\uDD04 Fetching teams for league:\", leagueId, \"season:\", currentSeason);\n        // Use proxy endpoint through Next.js frontend with higher limit to get all teams\n        const timestamp = Date.now();\n        const response = await fetch(\"/api/teams?league=\".concat(leagueId, \"&season=\").concat(currentSeason, \"&limit=50&_t=\").concat(timestamp));\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch teams: \".concat(response.status));\n        }\n        const apiData = await response.json();\n        console.log(\"✅ Teams API response:\", apiData);\n        if (!apiData.data || !Array.isArray(apiData.data)) {\n            console.warn(\"⚠️ No teams data in API response, using fallback\");\n            return generateFallbackTeams(leagueId);\n        }\n        // Transform API data to our interface\n        return apiData.data.map((team)=>{\n            var _team_externalId, _team_id, _team_venue, _team_venue1, _team_venue2;\n            return {\n                id: ((_team_externalId = team.externalId) === null || _team_externalId === void 0 ? void 0 : _team_externalId.toString()) || ((_team_id = team.id) === null || _team_id === void 0 ? void 0 : _team_id.toString()) || Math.random().toString(),\n                name: team.name || \"Unknown Team\",\n                logo: team.logo || \"\",\n                foundedYear: team.founded || new Date().getFullYear(),\n                country: team.country || \"Unknown\",\n                city: ((_team_venue = team.venue) === null || _team_venue === void 0 ? void 0 : _team_venue.city) || \"Unknown\",\n                stadium: ((_team_venue1 = team.venue) === null || _team_venue1 === void 0 ? void 0 : _team_venue1.name) || \"Unknown Stadium\",\n                capacity: ((_team_venue2 = team.venue) === null || _team_venue2 === void 0 ? void 0 : _team_venue2.capacity) || 0,\n                website: \"\",\n                description: \"\",\n                stats: {\n                    matchesPlayed: 0,\n                    wins: 0,\n                    draws: 0,\n                    losses: 0,\n                    goalsFor: 0,\n                    goalsAgainst: 0,\n                    points: 0,\n                    position: undefined\n                },\n                recentForm: [],\n                manager: \"\",\n                playersCount: 0,\n                externalId: team.externalId,\n                code: team.code,\n                venue: team.venue ? {\n                    id: team.venue.id,\n                    name: team.venue.name,\n                    address: team.venue.address,\n                    city: team.venue.city,\n                    capacity: team.venue.capacity,\n                    surface: team.venue.surface,\n                    image: team.venue.image\n                } : undefined,\n                founded: team.founded\n            };\n        });\n    } catch (error) {\n        console.error(\"❌ Error fetching teams:\", error);\n        // Return fallback data on error\n        return generateFallbackTeams(leagueId);\n    }\n};\n// Fallback teams data when API fails\nconst generateFallbackTeams = (leagueId)=>{\n    const teamNames = [\n        \"Arsenal FC\",\n        \"Manchester United\",\n        \"Liverpool FC\",\n        \"Chelsea FC\",\n        \"Manchester City\",\n        \"Tottenham Hotspur\",\n        \"Newcastle United\",\n        \"Brighton & Hove\",\n        \"Aston Villa\",\n        \"West Ham United\",\n        \"Crystal Palace\",\n        \"Fulham FC\",\n        \"Brentford FC\",\n        \"Wolverhampton\",\n        \"Everton FC\",\n        \"Nottingham Forest\",\n        \"Bournemouth AFC\",\n        \"Sheffield United\",\n        \"Burnley FC\",\n        \"Luton Town\"\n    ];\n    const countries = [\n        \"England\",\n        \"Scotland\",\n        \"Wales\",\n        \"Ireland\"\n    ];\n    const cities = [\n        \"London\",\n        \"Manchester\",\n        \"Liverpool\",\n        \"Birmingham\",\n        \"Newcastle\",\n        \"Brighton\",\n        \"Sheffield\",\n        \"Burnley\",\n        \"Luton\",\n        \"Bournemouth\"\n    ];\n    const stadiums = [\n        \"Emirates Stadium\",\n        \"Old Trafford\",\n        \"Anfield\",\n        \"Stamford Bridge\",\n        \"Etihad Stadium\",\n        \"Tottenham Hotspur Stadium\",\n        \"St. James' Park\",\n        \"American Express Community Stadium\",\n        \"Villa Park\",\n        \"London Stadium\"\n    ];\n    const managers = [\n        \"Mikel Arteta\",\n        \"Erik ten Hag\",\n        \"J\\xfcrgen Klopp\",\n        \"Mauricio Pochettino\",\n        \"Pep Guardiola\",\n        \"Ange Postecoglou\",\n        \"Eddie Howe\",\n        \"Roberto De Zerbi\",\n        \"Unai Emery\",\n        \"David Moyes\",\n        \"Roy Hodgson\",\n        \"Marco Silva\"\n    ];\n    return teamNames.map((name, index)=>{\n        const matchesPlayed = Math.floor(Math.random() * 38) + 10;\n        const wins = Math.floor(Math.random() * matchesPlayed * 0.6);\n        const losses = Math.floor(Math.random() * (matchesPlayed - wins) * 0.7);\n        const draws = matchesPlayed - wins - losses;\n        const points = wins * 3 + draws;\n        const goalsFor = Math.floor(Math.random() * 80) + 20;\n        const goalsAgainst = Math.floor(Math.random() * 60) + 15;\n        // Generate recent form (last 5 matches)\n        const recentForm = [];\n        for(let i = 0; i < 5; i++){\n            const rand = Math.random();\n            if (rand < 0.4) recentForm.push(\"W\");\n            else if (rand < 0.7) recentForm.push(\"D\");\n            else recentForm.push(\"L\");\n        }\n        return {\n            id: \"team-\".concat(leagueId, \"-\").concat(index + 1),\n            name,\n            logo: \"/images/teams/\".concat(name.toLowerCase().replace(/\\s+/g, \"-\"), \".png\"),\n            foundedYear: Math.floor(Math.random() * 120) + 1880,\n            country: countries[Math.floor(Math.random() * countries.length)],\n            city: cities[Math.floor(Math.random() * cities.length)],\n            stadium: stadiums[Math.floor(Math.random() * stadiums.length)],\n            capacity: Math.floor(Math.random() * 60000) + 20000,\n            website: \"https://\".concat(name.toLowerCase().replace(/\\s+/g, \"\"), \".com\"),\n            description: \"\".concat(name, \" is one of the most prestigious football clubs with a rich history and passionate fanbase.\"),\n            stats: {\n                matchesPlayed,\n                wins,\n                draws,\n                losses,\n                goalsFor,\n                goalsAgainst,\n                points,\n                position: index + 1\n            },\n            recentForm,\n            manager: managers[Math.floor(Math.random() * managers.length)],\n            playersCount: Math.floor(Math.random() * 10) + 25\n        };\n    }).sort((a, b)=>b.stats.points - a.stats.points); // Sort by points initially\n};\nconst useLeagueTeams = (param)=>{\n    let { leagueId, season } = param;\n    const [teams, setTeams] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"position\");\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"asc\");\n    const [countryFilter, setCountryFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n    // Simulate API call\n    const fetchTeams = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Fetch teams from real API\n            const apiTeams = await fetchTeamsFromAPI(leagueId, season);\n            setTeams(apiTeams);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to fetch teams\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (leagueId) {\n            // Clear any existing data first\n            setTeams([]);\n            setError(null);\n            fetchTeams();\n        }\n    }, [\n        leagueId,\n        season\n    ]);\n    // Filter and sort teams\n    const filteredAndSortedTeams = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        let filtered = teams;\n        // Apply search filter\n        if (searchTerm) {\n            filtered = filtered.filter((team)=>team.name.toLowerCase().includes(searchTerm.toLowerCase()) || team.country.toLowerCase().includes(searchTerm.toLowerCase()) || team.city.toLowerCase().includes(searchTerm.toLowerCase()));\n        }\n        // Apply country filter\n        if (countryFilter) {\n            filtered = filtered.filter((team)=>team.country === countryFilter);\n        }\n        // Apply sorting\n        const sorted = [\n            ...filtered\n        ].sort((a, b)=>{\n            let aValue, bValue;\n            switch(sortBy){\n                case \"name\":\n                    aValue = a.name.toLowerCase();\n                    bValue = b.name.toLowerCase();\n                    break;\n                case \"foundedYear\":\n                    aValue = a.foundedYear;\n                    bValue = b.foundedYear;\n                    break;\n                case \"country\":\n                    aValue = a.country.toLowerCase();\n                    bValue = b.country.toLowerCase();\n                    break;\n                case \"points\":\n                    aValue = a.stats.points;\n                    bValue = b.stats.points;\n                    break;\n                case \"position\":\n                    aValue = a.stats.position || 999;\n                    bValue = b.stats.position || 999;\n                    break;\n                default:\n                    return 0;\n            }\n            if (aValue < bValue) return sortOrder === \"asc\" ? -1 : 1;\n            if (aValue > bValue) return sortOrder === \"asc\" ? 1 : -1;\n            return 0;\n        });\n        return sorted;\n    }, [\n        teams,\n        searchTerm,\n        countryFilter,\n        sortBy,\n        sortOrder\n    ]);\n    // Get unique countries\n    const countries = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const uniqueCountries = Array.from(new Set(teams.map((team)=>team.country)));\n        return uniqueCountries.sort();\n    }, [\n        teams\n    ]);\n    // Calculate statistics\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const totalTeams = teams.length;\n        const totalCountries = countries.length;\n        const averageFoundedYear = totalTeams > 0 ? Math.round(teams.reduce((sum, team)=>sum + team.foundedYear, 0) / totalTeams) : 0;\n        const totalMatches = teams.reduce((sum, team)=>sum + team.stats.matchesPlayed, 0);\n        return {\n            totalTeams,\n            totalCountries,\n            averageFoundedYear,\n            totalMatches\n        };\n    }, [\n        teams,\n        countries\n    ]);\n    const refreshTeams = async ()=>{\n        await fetchTeams();\n    };\n    return {\n        teams,\n        loading,\n        error,\n        searchTerm,\n        setSearchTerm,\n        sortBy,\n        setSortBy,\n        sortOrder,\n        setSortOrder,\n        countryFilter,\n        setCountryFilter,\n        filteredAndSortedTeams,\n        countries,\n        stats,\n        refreshTeams\n    };\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (useLeagueTeams);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/hooks/leagues/useLeagueTeams.ts\n"));

/***/ })

});