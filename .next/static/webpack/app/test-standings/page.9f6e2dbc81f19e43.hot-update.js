"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-standings/page",{

/***/ "(app-pages-browser)/./src/lib/hooks/leagues/useLeagueStandings.ts":
/*!*****************************************************!*\
  !*** ./src/lib/hooks/leagues/useLeagueStandings.ts ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLeagueStandings: function() { return /* binding */ useLeagueStandings; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useLeagueStandings auto */ \n// Fetch standings from API\nconst fetchStandingsFromAPI = async (leagueId, season, format)=>{\n    try {\n        var _apiData_data, _apiData_data1;\n        const currentSeason = season || new Date().getFullYear();\n        const formatParam = format ? \"&format=\".concat(format) : \"\";\n        console.log(\"\\uD83D\\uDD04 Fetching standings for league:\", leagueId, \"season:\", currentSeason);\n        // Use proxy endpoint through Next.js frontend with cache busting\n        const timestamp = Date.now();\n        const apiUrl = \"/api/standings?league=\".concat(leagueId, \"&season=\").concat(currentSeason).concat(formatParam, \"&_t=\").concat(timestamp);\n        console.log(\"\\uD83C\\uDF10 Making API call to:\", apiUrl);\n        const response = await fetch(apiUrl);\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch standings: \".concat(response.status));\n        }\n        const apiData = await response.json();\n        console.log(\"✅ Standings API response:\", apiData);\n        console.log(\"\\uD83D\\uDD0D API data structure check:\", {\n            hasData: !!apiData.data,\n            isArray: Array.isArray(apiData.data),\n            dataLength: (_apiData_data = apiData.data) === null || _apiData_data === void 0 ? void 0 : _apiData_data.length,\n            firstItem: (_apiData_data1 = apiData.data) === null || _apiData_data1 === void 0 ? void 0 : _apiData_data1[0]\n        });\n        if (!apiData.data || !Array.isArray(apiData.data)) {\n            console.warn(\"⚠️ No standings data in API response, using fallback\");\n            return generateFallbackStandings(leagueId);\n        }\n        // Transform API data to our interface\n        console.log(\"\\uD83D\\uDD04 Transforming API data to interface, count:\", apiData.data.length);\n        const transformedData = apiData.data.map((standing, index)=>{\n            var _standing_teamId, _standing_team_externalId, _standing_team, _standing_team1, _standing_team2, _standing_goals, _standing_goals1, _standing_team3, _standing_team4, _standing_team5;\n            // Parse form data from API (e.g., \"WWWWW\" -> ['W', 'W', 'W', 'W', 'W'])\n            let form = [];\n            if (standing.form && typeof standing.form === \"string\") {\n                form = standing.form.split(\"\").slice(0, 5);\n            } else {\n                // Fallback to random form if not provided\n                const formResults = [\n                    \"W\",\n                    \"L\",\n                    \"D\"\n                ];\n                form = Array.from({\n                    length: 5\n                }, ()=>formResults[Math.floor(Math.random() * formResults.length)]);\n            }\n            return {\n                position: standing.position || standing.rank || index + 1,\n                team: {\n                    id: ((_standing_teamId = standing.teamId) === null || _standing_teamId === void 0 ? void 0 : _standing_teamId.toString()) || ((_standing_team = standing.team) === null || _standing_team === void 0 ? void 0 : (_standing_team_externalId = _standing_team.externalId) === null || _standing_team_externalId === void 0 ? void 0 : _standing_team_externalId.toString()) || (index + 1).toString(),\n                    name: standing.teamName || ((_standing_team1 = standing.team) === null || _standing_team1 === void 0 ? void 0 : _standing_team1.name) || \"Team \".concat(index + 1),\n                    logo: standing.teamLogo || ((_standing_team2 = standing.team) === null || _standing_team2 === void 0 ? void 0 : _standing_team2.logo) || \"\"\n                },\n                points: standing.points || 0,\n                playedGames: standing.played || standing.playedGames || 0,\n                wins: standing.win || standing.wins || 0,\n                draws: standing.draw || standing.draws || 0,\n                losses: standing.lose || standing.losses || 0,\n                goalsFor: standing.goalsFor || ((_standing_goals = standing.goals) === null || _standing_goals === void 0 ? void 0 : _standing_goals.for) || 0,\n                goalsAgainst: standing.goalsAgainst || ((_standing_goals1 = standing.goals) === null || _standing_goals1 === void 0 ? void 0 : _standing_goals1.against) || 0,\n                goalDifference: standing.goalsDiff !== undefined ? standing.goalsDiff : standing.goalDifference !== undefined ? standing.goalDifference : (standing.goalsFor || 0) - (standing.goalsAgainst || 0),\n                form,\n                // Store original API fields\n                externalId: standing.externalId || standing.id,\n                teamId: standing.teamId || ((_standing_team3 = standing.team) === null || _standing_team3 === void 0 ? void 0 : _standing_team3.externalId),\n                teamName: standing.teamName || ((_standing_team4 = standing.team) === null || _standing_team4 === void 0 ? void 0 : _standing_team4.name),\n                teamLogo: standing.teamLogo || ((_standing_team5 = standing.team) === null || _standing_team5 === void 0 ? void 0 : _standing_team5.logo)\n            };\n        });\n        const sortedData = transformedData.sort((a, b)=>a.position - b.position);\n        console.log(\"✅ Transformed standings data:\", sortedData.slice(0, 3).map((s)=>({\n                position: s.position,\n                teamName: s.team.name,\n                points: s.points\n            })));\n        return sortedData;\n    } catch (error) {\n        console.error(\"❌ Error fetching standings:\", error);\n        throw error;\n    }\n};\n// Generate fallback standings data for development/testing\nconst generateFallbackStandings = (leagueId)=>{\n    const teams = [\n        {\n            id: \"33\",\n            name: \"Manchester United\",\n            logo: \"https://media.api-sports.io/football/teams/33.png\"\n        },\n        {\n            id: \"50\",\n            name: \"Manchester City\",\n            logo: \"https://media.api-sports.io/football/teams/50.png\"\n        },\n        {\n            id: \"42\",\n            name: \"Arsenal\",\n            logo: \"https://media.api-sports.io/football/teams/42.png\"\n        },\n        {\n            id: \"40\",\n            name: \"Liverpool\",\n            logo: \"https://media.api-sports.io/football/teams/40.png\"\n        },\n        {\n            id: \"49\",\n            name: \"Chelsea\",\n            logo: \"https://media.api-sports.io/football/teams/49.png\"\n        },\n        {\n            id: \"47\",\n            name: \"Tottenham\",\n            logo: \"https://media.api-sports.io/football/teams/47.png\"\n        },\n        {\n            id: \"34\",\n            name: \"Newcastle\",\n            logo: \"https://media.api-sports.io/football/teams/34.png\"\n        },\n        {\n            id: \"66\",\n            name: \"Aston Villa\",\n            logo: \"https://media.api-sports.io/football/teams/66.png\"\n        },\n        {\n            id: \"51\",\n            name: \"Brighton\",\n            logo: \"https://media.api-sports.io/football/teams/51.png\"\n        },\n        {\n            id: \"39\",\n            name: \"Wolves\",\n            logo: \"https://media.api-sports.io/football/teams/39.png\"\n        }\n    ];\n    return teams.map((team, index)=>{\n        const played = 20 + Math.floor(Math.random() * 10);\n        const wins = Math.floor(Math.random() * played * 0.7);\n        const losses = Math.floor(Math.random() * (played - wins) * 0.6);\n        const draws = played - wins - losses;\n        const goalsFor = wins * 2 + draws + Math.floor(Math.random() * 10);\n        const goalsAgainst = losses * 2 + Math.floor(Math.random() * goalsFor * 0.8);\n        // Generate realistic form (last 5 matches)\n        const formResults = [\n            \"W\",\n            \"L\",\n            \"D\"\n        ];\n        const form = Array.from({\n            length: 5\n        }, ()=>formResults[Math.floor(Math.random() * formResults.length)]);\n        return {\n            position: index + 1,\n            team,\n            points: wins * 3 + draws,\n            playedGames: played,\n            wins,\n            draws,\n            losses,\n            goalsFor,\n            goalsAgainst,\n            goalDifference: goalsFor - goalsAgainst,\n            form,\n            externalId: parseInt(team.id),\n            teamId: parseInt(team.id),\n            teamName: team.name,\n            teamLogo: team.logo\n        };\n    }).sort((a, b)=>b.points - a.points || b.goalDifference - a.goalDifference);\n};\nconst useLeagueStandings = (param)=>{\n    let { leagueId, season, format = \"external\" } = param;\n    const [standings, setStandings] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    console.log(\"\\uD83C\\uDFD7️ useLeagueStandings hook initialized:\", {\n        leagueId,\n        season,\n        format\n    });\n    const refreshStandings = async ()=>{\n        try {\n            var _standingsData__team, _standingsData_;\n            setLoading(true);\n            setError(null);\n            const standingsData = await fetchStandingsFromAPI(leagueId, season, format);\n            console.log(\"\\uD83C\\uDFAF Setting standings data in hook:\", standingsData.length, \"teams\");\n            console.log(\"\\uD83C\\uDFAF First team in standings:\", (_standingsData_ = standingsData[0]) === null || _standingsData_ === void 0 ? void 0 : (_standingsData__team = _standingsData_.team) === null || _standingsData__team === void 0 ? void 0 : _standingsData__team.name);\n            setStandings(standingsData);\n        } catch (err) {\n            console.error(\"❌ Error fetching standings:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to fetch standings\");\n            // Use fallback data in case of error\n            console.warn(\"⚠️ Using fallback standings data due to error\");\n            setStandings(generateFallbackStandings(leagueId));\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (leagueId) {\n            console.log(\"\\uD83D\\uDD04 useLeagueStandings useEffect triggered:\", {\n                leagueId,\n                season,\n                format\n            });\n            // Clear any existing data first\n            setStandings([]);\n            setError(null);\n            refreshStandings();\n        }\n    }, [\n        leagueId,\n        season,\n        format\n    ]);\n    // Calculate statistics\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const totalTeams = standings.length;\n        const topTeam = standings.length > 0 ? standings[0] : null;\n        const totalGames = standings.reduce((sum, team)=>sum + team.playedGames, 0);\n        const totalGoals = standings.reduce((sum, team)=>sum + team.goalsFor, 0);\n        const avgGoalsPerGame = totalGames > 0 ? Math.round(totalGoals / totalGames * 100) / 100 : 0;\n        return {\n            totalTeams,\n            topTeam,\n            avgGoalsPerGame\n        };\n    }, [\n        standings\n    ]);\n    return {\n        standings,\n        loading,\n        error,\n        refreshStandings,\n        stats\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9saWIvaG9va3MvbGVhZ3Vlcy91c2VMZWFndWVTdGFuZGluZ3MudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O3dFQUVxRDtBQTJDckQsMkJBQTJCO0FBQzNCLE1BQU1HLHdCQUF3QixPQUFPQyxVQUFrQkMsUUFBaUJDO0lBQ2xFLElBQUk7WUFvQm9CQyxlQUNEQTtRQXBCakIsTUFBTUMsZ0JBQWdCSCxVQUFVLElBQUlJLE9BQU9DLFdBQVc7UUFDdEQsTUFBTUMsY0FBY0wsU0FBUyxXQUFrQixPQUFQQSxVQUFXO1FBQ25ETSxRQUFRQyxHQUFHLENBQUMsK0NBQXFDVCxVQUFVLFdBQVdJO1FBRXRFLGlFQUFpRTtRQUNqRSxNQUFNTSxZQUFZTCxLQUFLTSxHQUFHO1FBQzFCLE1BQU1DLFNBQVMseUJBQTRDUixPQUFuQkosVUFBUyxZQUEwQk8sT0FBaEJILGVBQWtDTSxPQUFsQkgsYUFBWSxRQUFnQixPQUFWRztRQUM3RkYsUUFBUUMsR0FBRyxDQUFDLG9DQUEwQkc7UUFDdEMsTUFBTUMsV0FBVyxNQUFNQyxNQUFNRjtRQUU3QixJQUFJLENBQUNDLFNBQVNFLEVBQUUsRUFBRTtZQUNaLE1BQU0sSUFBSUMsTUFBTSw4QkFBOEMsT0FBaEJILFNBQVNJLE1BQU07UUFDbkU7UUFFQSxNQUFNZCxVQUFVLE1BQU1VLFNBQVNLLElBQUk7UUFDbkNWLFFBQVFDLEdBQUcsQ0FBQyw2QkFBNkJOO1FBQ3pDSyxRQUFRQyxHQUFHLENBQUMsMENBQWdDO1lBQ3RDVSxTQUFTLENBQUMsQ0FBQ2hCLFFBQVFpQixJQUFJO1lBQ3ZCQyxTQUFTQyxNQUFNRCxPQUFPLENBQUNsQixRQUFRaUIsSUFBSTtZQUNuQ0csVUFBVSxHQUFFcEIsZ0JBQUFBLFFBQVFpQixJQUFJLGNBQVpqQixvQ0FBQUEsY0FBY3FCLE1BQU07WUFDaENDLFNBQVMsR0FBRXRCLGlCQUFBQSxRQUFRaUIsSUFBSSxjQUFaakIscUNBQUFBLGNBQWMsQ0FBQyxFQUFFO1FBQ2xDO1FBRUEsSUFBSSxDQUFDQSxRQUFRaUIsSUFBSSxJQUFJLENBQUNFLE1BQU1ELE9BQU8sQ0FBQ2xCLFFBQVFpQixJQUFJLEdBQUc7WUFDN0NaLFFBQVFrQixJQUFJLENBQUM7WUFDYixPQUFPQywwQkFBMEIzQjtRQUN2QztRQUVBLHNDQUFzQztRQUN0Q1EsUUFBUUMsR0FBRyxDQUFDLDJEQUFpRE4sUUFBUWlCLElBQUksQ0FBQ0ksTUFBTTtRQUNoRixNQUFNSSxrQkFBa0J6QixRQUFRaUIsSUFBSSxDQUFDUyxHQUFHLENBQUMsQ0FBQ0MsVUFBZUM7Z0JBZ0JuQ0Qsa0JBQStCQSwyQkFBQUEsZ0JBQ1JBLGlCQUNBQSxpQkFPRkEsaUJBQ1FBLGtCQVNaQSxpQkFDSUEsaUJBQ0FBO1lBcENyQyx3RUFBd0U7WUFDeEUsSUFBSUUsT0FBNEIsRUFBRTtZQUNsQyxJQUFJRixTQUFTRSxJQUFJLElBQUksT0FBT0YsU0FBU0UsSUFBSSxLQUFLLFVBQVU7Z0JBQ2xEQSxPQUFPRixTQUFTRSxJQUFJLENBQUNDLEtBQUssQ0FBQyxJQUFJQyxLQUFLLENBQUMsR0FBRztZQUM5QyxPQUFPO2dCQUNELDBDQUEwQztnQkFDMUMsTUFBTUMsY0FBYztvQkFBQztvQkFBSztvQkFBSztpQkFBSTtnQkFDbkNILE9BQU9WLE1BQU1jLElBQUksQ0FBQztvQkFBRVosUUFBUTtnQkFBRSxHQUFHLElBQzNCVyxXQUFXLENBQUNFLEtBQUtDLEtBQUssQ0FBQ0QsS0FBS0UsTUFBTSxLQUFLSixZQUFZWCxNQUFNLEVBQUU7WUFFdkU7WUFFQSxPQUFPO2dCQUNEZ0IsVUFBVVYsU0FBU1UsUUFBUSxJQUFJVixTQUFTVyxJQUFJLElBQUtWLFFBQVE7Z0JBQ3pEVyxNQUFNO29CQUNBQyxJQUFJYixFQUFBQSxtQkFBQUEsU0FBU2MsTUFBTSxjQUFmZCx1Q0FBQUEsaUJBQWlCZSxRQUFRLFNBQU1mLGlCQUFBQSxTQUFTWSxJQUFJLGNBQWJaLHNDQUFBQSw0QkFBQUEsZUFBZWdCLFVBQVUsY0FBekJoQixnREFBQUEsMEJBQTJCZSxRQUFRLE9BQU0sQ0FBQ2QsUUFBUSxHQUFHYyxRQUFRO29CQUNoR0UsTUFBTWpCLFNBQVNrQixRQUFRLE1BQUlsQixrQkFBQUEsU0FBU1ksSUFBSSxjQUFiWixzQ0FBQUEsZ0JBQWVpQixJQUFJLEtBQUksUUFBa0IsT0FBVmhCLFFBQVE7b0JBQ2xFa0IsTUFBTW5CLFNBQVNvQixRQUFRLE1BQUlwQixrQkFBQUEsU0FBU1ksSUFBSSxjQUFiWixzQ0FBQUEsZ0JBQWVtQixJQUFJLEtBQUk7Z0JBQ3hEO2dCQUNBRSxRQUFRckIsU0FBU3FCLE1BQU0sSUFBSTtnQkFDM0JDLGFBQWF0QixTQUFTdUIsTUFBTSxJQUFJdkIsU0FBU3NCLFdBQVcsSUFBSTtnQkFDeERFLE1BQU14QixTQUFTeUIsR0FBRyxJQUFJekIsU0FBU3dCLElBQUksSUFBSTtnQkFDdkNFLE9BQU8xQixTQUFTMkIsSUFBSSxJQUFJM0IsU0FBUzBCLEtBQUssSUFBSTtnQkFDMUNFLFFBQVE1QixTQUFTNkIsSUFBSSxJQUFJN0IsU0FBUzRCLE1BQU0sSUFBSTtnQkFDNUNFLFVBQVU5QixTQUFTOEIsUUFBUSxNQUFJOUIsa0JBQUFBLFNBQVMrQixLQUFLLGNBQWQvQixzQ0FBQUEsZ0JBQWdCZ0MsR0FBRyxLQUFJO2dCQUN0REMsY0FBY2pDLFNBQVNpQyxZQUFZLE1BQUlqQyxtQkFBQUEsU0FBUytCLEtBQUssY0FBZC9CLHVDQUFBQSxpQkFBZ0JrQyxPQUFPLEtBQUk7Z0JBQ2xFQyxnQkFBZ0IsU0FBVUMsU0FBUyxLQUFLQyxZQUNoQ3JDLFNBQVNvQyxTQUFTLEdBQ2xCLFNBQVVELGNBQWMsS0FBS0UsWUFDdkJyQyxTQUFTbUMsY0FBYyxHQUN2QixDQUFDbkMsU0FBUzhCLFFBQVEsSUFBSSxLQUFNOUIsQ0FBQUEsU0FBU2lDLFlBQVksSUFBSTtnQkFDbkUvQjtnQkFDQSw0QkFBNEI7Z0JBQzVCYyxZQUFZaEIsU0FBU2dCLFVBQVUsSUFBSWhCLFNBQVNhLEVBQUU7Z0JBQzlDQyxRQUFRZCxTQUFTYyxNQUFNLE1BQUlkLGtCQUFBQSxTQUFTWSxJQUFJLGNBQWJaLHNDQUFBQSxnQkFBZWdCLFVBQVU7Z0JBQ3BERSxVQUFVbEIsU0FBU2tCLFFBQVEsTUFBSWxCLGtCQUFBQSxTQUFTWSxJQUFJLGNBQWJaLHNDQUFBQSxnQkFBZWlCLElBQUk7Z0JBQ2xERyxVQUFVcEIsU0FBU29CLFFBQVEsTUFBSXBCLGtCQUFBQSxTQUFTWSxJQUFJLGNBQWJaLHNDQUFBQSxnQkFBZW1CLElBQUk7WUFDeEQ7UUFDTjtRQUVBLE1BQU1tQixhQUFheEMsZ0JBQWdCeUMsSUFBSSxDQUFDLENBQUNDLEdBQWlCQyxJQUFvQkQsRUFBRTlCLFFBQVEsR0FBRytCLEVBQUUvQixRQUFRO1FBQ3JHaEMsUUFBUUMsR0FBRyxDQUFDLGlDQUFpQzJELFdBQVdsQyxLQUFLLENBQUMsR0FBRyxHQUFHTCxHQUFHLENBQUMyQyxDQUFBQSxJQUFNO2dCQUFFaEMsVUFBVWdDLEVBQUVoQyxRQUFRO2dCQUFFUSxVQUFVd0IsRUFBRTlCLElBQUksQ0FBQ0ssSUFBSTtnQkFBRUksUUFBUXFCLEVBQUVyQixNQUFNO1lBQUM7UUFDOUksT0FBT2lCO0lBRWIsRUFBRSxPQUFPSyxPQUFPO1FBQ1ZqRSxRQUFRaUUsS0FBSyxDQUFDLCtCQUErQkE7UUFDN0MsTUFBTUE7SUFDWjtBQUNOO0FBRUEsMkRBQTJEO0FBQzNELE1BQU05Qyw0QkFBNEIsQ0FBQzNCO0lBQzdCLE1BQU0wRSxRQUFRO1FBQ1I7WUFBRS9CLElBQUk7WUFBTUksTUFBTTtZQUFxQkUsTUFBTTtRQUFvRDtRQUNqRztZQUFFTixJQUFJO1lBQU1JLE1BQU07WUFBbUJFLE1BQU07UUFBb0Q7UUFDL0Y7WUFBRU4sSUFBSTtZQUFNSSxNQUFNO1lBQVdFLE1BQU07UUFBb0Q7UUFDdkY7WUFBRU4sSUFBSTtZQUFNSSxNQUFNO1lBQWFFLE1BQU07UUFBb0Q7UUFDekY7WUFBRU4sSUFBSTtZQUFNSSxNQUFNO1lBQVdFLE1BQU07UUFBb0Q7UUFDdkY7WUFBRU4sSUFBSTtZQUFNSSxNQUFNO1lBQWFFLE1BQU07UUFBb0Q7UUFDekY7WUFBRU4sSUFBSTtZQUFNSSxNQUFNO1lBQWFFLE1BQU07UUFBb0Q7UUFDekY7WUFBRU4sSUFBSTtZQUFNSSxNQUFNO1lBQWVFLE1BQU07UUFBb0Q7UUFDM0Y7WUFBRU4sSUFBSTtZQUFNSSxNQUFNO1lBQVlFLE1BQU07UUFBb0Q7UUFDeEY7WUFBRU4sSUFBSTtZQUFNSSxNQUFNO1lBQVVFLE1BQU07UUFBb0Q7S0FDM0Y7SUFFRCxPQUFPeUIsTUFBTTdDLEdBQUcsQ0FBQyxDQUFDYSxNQUFNWDtRQUNsQixNQUFNc0IsU0FBUyxLQUFLaEIsS0FBS0MsS0FBSyxDQUFDRCxLQUFLRSxNQUFNLEtBQUs7UUFDL0MsTUFBTWUsT0FBT2pCLEtBQUtDLEtBQUssQ0FBQ0QsS0FBS0UsTUFBTSxLQUFLYyxTQUFTO1FBQ2pELE1BQU1LLFNBQVNyQixLQUFLQyxLQUFLLENBQUNELEtBQUtFLE1BQU0sS0FBTWMsQ0FBQUEsU0FBU0MsSUFBRyxJQUFLO1FBQzVELE1BQU1FLFFBQVFILFNBQVNDLE9BQU9JO1FBQzlCLE1BQU1FLFdBQVdOLE9BQU8sSUFBSUUsUUFBUW5CLEtBQUtDLEtBQUssQ0FBQ0QsS0FBS0UsTUFBTSxLQUFLO1FBQy9ELE1BQU13QixlQUFlTCxTQUFTLElBQUlyQixLQUFLQyxLQUFLLENBQUNELEtBQUtFLE1BQU0sS0FBS3FCLFdBQVc7UUFFeEUsMkNBQTJDO1FBQzNDLE1BQU16QixjQUFjO1lBQUM7WUFBSztZQUFLO1NBQUk7UUFDbkMsTUFBTUgsT0FBT1YsTUFBTWMsSUFBSSxDQUFDO1lBQUVaLFFBQVE7UUFBRSxHQUFHLElBQ2pDVyxXQUFXLENBQUNFLEtBQUtDLEtBQUssQ0FBQ0QsS0FBS0UsTUFBTSxLQUFLSixZQUFZWCxNQUFNLEVBQUU7UUFHakUsT0FBTztZQUNEZ0IsVUFBVVQsUUFBUTtZQUNsQlc7WUFDQVMsUUFBUUcsT0FBTyxJQUFJRTtZQUNuQkosYUFBYUM7WUFDYkM7WUFDQUU7WUFDQUU7WUFDQUU7WUFDQUc7WUFDQUUsZ0JBQWdCTCxXQUFXRztZQUMzQi9CO1lBQ0FjLFlBQVk2QixTQUFTakMsS0FBS0MsRUFBRTtZQUM1QkMsUUFBUStCLFNBQVNqQyxLQUFLQyxFQUFFO1lBQ3hCSyxVQUFVTixLQUFLSyxJQUFJO1lBQ25CRyxVQUFVUixLQUFLTyxJQUFJO1FBQ3pCO0lBQ04sR0FBR29CLElBQUksQ0FBQyxDQUFDQyxHQUFHQyxJQUFNQSxFQUFFcEIsTUFBTSxHQUFHbUIsRUFBRW5CLE1BQU0sSUFBSW9CLEVBQUVOLGNBQWMsR0FBR0ssRUFBRUwsY0FBYztBQUNsRjtBQUVPLE1BQU1XLHFCQUFxQjtRQUFDLEVBQzdCNUUsUUFBUSxFQUNSQyxNQUFNLEVBQ05DLFNBQVMsVUFBVSxFQUNDO0lBQ3BCLE1BQU0sQ0FBQzJFLFdBQVdDLGFBQWEsR0FBR2xGLCtDQUFRQSxDQUFpQixFQUFFO0lBQzdELE1BQU0sQ0FBQ21GLFNBQVNDLFdBQVcsR0FBR3BGLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQzZFLE9BQU9RLFNBQVMsR0FBR3JGLCtDQUFRQSxDQUFnQjtJQUVsRFksUUFBUUMsR0FBRyxDQUFDLHNEQUE0QztRQUFFVDtRQUFVQztRQUFRQztJQUFPO0lBRW5GLE1BQU1nRixtQkFBbUI7UUFDbkIsSUFBSTtnQkFLNkNDLHNCQUFBQTtZQUozQ0gsV0FBVztZQUNYQyxTQUFTO1lBQ1QsTUFBTUUsZ0JBQWdCLE1BQU1wRixzQkFBc0JDLFVBQVVDLFFBQVFDO1lBQ3BFTSxRQUFRQyxHQUFHLENBQUMsZ0RBQXNDMEUsY0FBYzNELE1BQU0sRUFBRTtZQUN4RWhCLFFBQVFDLEdBQUcsQ0FBQywwQ0FBK0IwRSxrQkFBQUEsYUFBYSxDQUFDLEVBQUUsY0FBaEJBLHVDQUFBQSx1QkFBQUEsZ0JBQWtCekMsSUFBSSxjQUF0QnlDLDJDQUFBQSxxQkFBd0JwQyxJQUFJO1lBQ3ZFK0IsYUFBYUs7UUFDbkIsRUFBRSxPQUFPQyxLQUFLO1lBQ1I1RSxRQUFRaUUsS0FBSyxDQUFDLCtCQUErQlc7WUFDN0NILFNBQVNHLGVBQWVwRSxRQUFRb0UsSUFBSUMsT0FBTyxHQUFHO1lBQzlDLHFDQUFxQztZQUNyQzdFLFFBQVFrQixJQUFJLENBQUM7WUFDYm9ELGFBQWFuRCwwQkFBMEIzQjtRQUM3QyxTQUFVO1lBQ0pnRixXQUFXO1FBQ2pCO0lBQ047SUFFQW5GLGdEQUFTQSxDQUFDO1FBQ0osSUFBSUcsVUFBVTtZQUNSUSxRQUFRQyxHQUFHLENBQUMsd0RBQThDO2dCQUFFVDtnQkFBVUM7Z0JBQVFDO1lBQU87WUFDckYsZ0NBQWdDO1lBQ2hDNEUsYUFBYSxFQUFFO1lBQ2ZHLFNBQVM7WUFDVEM7UUFDTjtJQUNOLEdBQUc7UUFBQ2xGO1FBQVVDO1FBQVFDO0tBQU87SUFFN0IsdUJBQXVCO0lBQ3ZCLE1BQU1vRixRQUFReEYsOENBQU9BLENBQUM7UUFDaEIsTUFBTXlGLGFBQWFWLFVBQVVyRCxNQUFNO1FBQ25DLE1BQU1nRSxVQUFVWCxVQUFVckQsTUFBTSxHQUFHLElBQUlxRCxTQUFTLENBQUMsRUFBRSxHQUFHO1FBRXRELE1BQU1ZLGFBQWFaLFVBQVVhLE1BQU0sQ0FBQyxDQUFDQyxLQUFLakQsT0FBU2lELE1BQU1qRCxLQUFLVSxXQUFXLEVBQUU7UUFDM0UsTUFBTXdDLGFBQWFmLFVBQVVhLE1BQU0sQ0FBQyxDQUFDQyxLQUFLakQsT0FBU2lELE1BQU1qRCxLQUFLa0IsUUFBUSxFQUFFO1FBQ3hFLE1BQU1pQyxrQkFBa0JKLGFBQWEsSUFBSXBELEtBQUt5RCxLQUFLLENBQUMsYUFBY0wsYUFBYyxPQUFPLE1BQU07UUFFN0YsT0FBTztZQUNERjtZQUNBQztZQUNBSztRQUNOO0lBQ04sR0FBRztRQUFDaEI7S0FBVTtJQUVkLE9BQU87UUFDREE7UUFDQUU7UUFDQU47UUFDQVM7UUFDQUk7SUFDTjtBQUNOLEVBQUUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2xpYi9ob29rcy9sZWFndWVzL3VzZUxlYWd1ZVN0YW5kaW5ncy50cz8zZWI2Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlTWVtbyB9IGZyb20gJ3JlYWN0JztcblxuZXhwb3J0IGludGVyZmFjZSBTdGFuZGluZ0RhdGEge1xuICAgICAgcG9zaXRpb246IG51bWJlcjtcbiAgICAgIHRlYW06IHtcbiAgICAgICAgICAgIGlkOiBzdHJpbmc7XG4gICAgICAgICAgICBuYW1lOiBzdHJpbmc7XG4gICAgICAgICAgICBsb2dvPzogc3RyaW5nO1xuICAgICAgfTtcbiAgICAgIHBvaW50czogbnVtYmVyO1xuICAgICAgcGxheWVkR2FtZXM6IG51bWJlcjtcbiAgICAgIHdpbnM6IG51bWJlcjtcbiAgICAgIGRyYXdzOiBudW1iZXI7XG4gICAgICBsb3NzZXM6IG51bWJlcjtcbiAgICAgIGdvYWxzRm9yOiBudW1iZXI7XG4gICAgICBnb2Fsc0FnYWluc3Q6IG51bWJlcjtcbiAgICAgIGdvYWxEaWZmZXJlbmNlOiBudW1iZXI7XG4gICAgICBmb3JtOiAoJ1cnIHwgJ0wnIHwgJ0QnKVtdO1xuICAgICAgLy8gQVBJIGZpZWxkc1xuICAgICAgZXh0ZXJuYWxJZD86IG51bWJlcjtcbiAgICAgIHRlYW1JZD86IG51bWJlcjtcbiAgICAgIHRlYW1OYW1lPzogc3RyaW5nO1xuICAgICAgdGVhbUxvZ28/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgVXNlTGVhZ3VlU3RhbmRpbmdzUHJvcHMge1xuICAgICAgbGVhZ3VlSWQ6IHN0cmluZztcbiAgICAgIHNlYXNvbj86IG51bWJlcjtcbiAgICAgIGZvcm1hdD86ICdleHRlcm5hbCcgfCAnaW50ZXJuYWwnO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFVzZUxlYWd1ZVN0YW5kaW5nc1JldHVybiB7XG4gICAgICBzdGFuZGluZ3M6IFN0YW5kaW5nRGF0YVtdO1xuICAgICAgbG9hZGluZzogYm9vbGVhbjtcbiAgICAgIGVycm9yOiBzdHJpbmcgfCBudWxsO1xuICAgICAgcmVmcmVzaFN0YW5kaW5nczogKCkgPT4gdm9pZDtcbiAgICAgIHN0YXRzOiB7XG4gICAgICAgICAgICB0b3RhbFRlYW1zOiBudW1iZXI7XG4gICAgICAgICAgICB0b3BUZWFtOiBTdGFuZGluZ0RhdGEgfCBudWxsO1xuICAgICAgICAgICAgYXZnR29hbHNQZXJHYW1lOiBudW1iZXI7XG4gICAgICB9O1xufVxuXG4vLyBGZXRjaCBzdGFuZGluZ3MgZnJvbSBBUElcbmNvbnN0IGZldGNoU3RhbmRpbmdzRnJvbUFQSSA9IGFzeW5jIChsZWFndWVJZDogc3RyaW5nLCBzZWFzb24/OiBudW1iZXIsIGZvcm1hdD86IHN0cmluZyk6IFByb21pc2U8U3RhbmRpbmdEYXRhW10+ID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zdCBjdXJyZW50U2Vhc29uID0gc2Vhc29uIHx8IG5ldyBEYXRlKCkuZ2V0RnVsbFllYXIoKTtcbiAgICAgICAgICAgIGNvbnN0IGZvcm1hdFBhcmFtID0gZm9ybWF0ID8gYCZmb3JtYXQ9JHtmb3JtYXR9YCA6ICcnO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ/CflIQgRmV0Y2hpbmcgc3RhbmRpbmdzIGZvciBsZWFndWU6JywgbGVhZ3VlSWQsICdzZWFzb246JywgY3VycmVudFNlYXNvbik7XG5cbiAgICAgICAgICAgIC8vIFVzZSBwcm94eSBlbmRwb2ludCB0aHJvdWdoIE5leHQuanMgZnJvbnRlbmQgd2l0aCBjYWNoZSBidXN0aW5nXG4gICAgICAgICAgICBjb25zdCB0aW1lc3RhbXAgPSBEYXRlLm5vdygpO1xuICAgICAgICAgICAgY29uc3QgYXBpVXJsID0gYC9hcGkvc3RhbmRpbmdzP2xlYWd1ZT0ke2xlYWd1ZUlkfSZzZWFzb249JHtjdXJyZW50U2Vhc29ufSR7Zm9ybWF0UGFyYW19Jl90PSR7dGltZXN0YW1wfWA7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+MkCBNYWtpbmcgQVBJIGNhbGwgdG86JywgYXBpVXJsKTtcbiAgICAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYXBpVXJsKTtcblxuICAgICAgICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggc3RhbmRpbmdzOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgY29uc3QgYXBpRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfinIUgU3RhbmRpbmdzIEFQSSByZXNwb25zZTonLCBhcGlEYXRhKTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SNIEFQSSBkYXRhIHN0cnVjdHVyZSBjaGVjazonLCB7XG4gICAgICAgICAgICAgICAgICBoYXNEYXRhOiAhIWFwaURhdGEuZGF0YSxcbiAgICAgICAgICAgICAgICAgIGlzQXJyYXk6IEFycmF5LmlzQXJyYXkoYXBpRGF0YS5kYXRhKSxcbiAgICAgICAgICAgICAgICAgIGRhdGFMZW5ndGg6IGFwaURhdGEuZGF0YT8ubGVuZ3RoLFxuICAgICAgICAgICAgICAgICAgZmlyc3RJdGVtOiBhcGlEYXRhLmRhdGE/LlswXVxuICAgICAgICAgICAgfSk7XG5cbiAgICAgICAgICAgIGlmICghYXBpRGF0YS5kYXRhIHx8ICFBcnJheS5pc0FycmF5KGFwaURhdGEuZGF0YSkpIHtcbiAgICAgICAgICAgICAgICAgIGNvbnNvbGUud2Fybign4pqg77iPIE5vIHN0YW5kaW5ncyBkYXRhIGluIEFQSSByZXNwb25zZSwgdXNpbmcgZmFsbGJhY2snKTtcbiAgICAgICAgICAgICAgICAgIHJldHVybiBnZW5lcmF0ZUZhbGxiYWNrU3RhbmRpbmdzKGxlYWd1ZUlkKTtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLy8gVHJhbnNmb3JtIEFQSSBkYXRhIHRvIG91ciBpbnRlcmZhY2VcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SEIFRyYW5zZm9ybWluZyBBUEkgZGF0YSB0byBpbnRlcmZhY2UsIGNvdW50OicsIGFwaURhdGEuZGF0YS5sZW5ndGgpO1xuICAgICAgICAgICAgY29uc3QgdHJhbnNmb3JtZWREYXRhID0gYXBpRGF0YS5kYXRhLm1hcCgoc3RhbmRpbmc6IGFueSwgaW5kZXg6IG51bWJlcikgPT4ge1xuICAgICAgICAgICAgICAgICAgLy8gUGFyc2UgZm9ybSBkYXRhIGZyb20gQVBJIChlLmcuLCBcIldXV1dXXCIgLT4gWydXJywgJ1cnLCAnVycsICdXJywgJ1cnXSlcbiAgICAgICAgICAgICAgICAgIGxldCBmb3JtOiAoJ1cnIHwgJ0wnIHwgJ0QnKVtdID0gW107XG4gICAgICAgICAgICAgICAgICBpZiAoc3RhbmRpbmcuZm9ybSAmJiB0eXBlb2Ygc3RhbmRpbmcuZm9ybSA9PT0gJ3N0cmluZycpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGZvcm0gPSBzdGFuZGluZy5mb3JtLnNwbGl0KCcnKS5zbGljZSgwLCA1KSBhcyAoJ1cnIHwgJ0wnIHwgJ0QnKVtdO1xuICAgICAgICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIEZhbGxiYWNrIHRvIHJhbmRvbSBmb3JtIGlmIG5vdCBwcm92aWRlZFxuICAgICAgICAgICAgICAgICAgICAgICAgY29uc3QgZm9ybVJlc3VsdHMgPSBbJ1cnLCAnTCcsICdEJ107XG4gICAgICAgICAgICAgICAgICAgICAgICBmb3JtID0gQXJyYXkuZnJvbSh7IGxlbmd0aDogNSB9LCAoKSA9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZm9ybVJlc3VsdHNbTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogZm9ybVJlc3VsdHMubGVuZ3RoKV1cbiAgICAgICAgICAgICAgICAgICAgICAgICkgYXMgKCdXJyB8ICdMJyB8ICdEJylbXTtcbiAgICAgICAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHBvc2l0aW9uOiBzdGFuZGluZy5wb3NpdGlvbiB8fCBzdGFuZGluZy5yYW5rIHx8IChpbmRleCArIDEpLFxuICAgICAgICAgICAgICAgICAgICAgICAgdGVhbToge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaWQ6IHN0YW5kaW5nLnRlYW1JZD8udG9TdHJpbmcoKSB8fCBzdGFuZGluZy50ZWFtPy5leHRlcm5hbElkPy50b1N0cmluZygpIHx8IChpbmRleCArIDEpLnRvU3RyaW5nKCksXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBuYW1lOiBzdGFuZGluZy50ZWFtTmFtZSB8fCBzdGFuZGluZy50ZWFtPy5uYW1lIHx8IGBUZWFtICR7aW5kZXggKyAxfWAsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBsb2dvOiBzdGFuZGluZy50ZWFtTG9nbyB8fCBzdGFuZGluZy50ZWFtPy5sb2dvIHx8ICcnLFxuICAgICAgICAgICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHBvaW50czogc3RhbmRpbmcucG9pbnRzIHx8IDAsXG4gICAgICAgICAgICAgICAgICAgICAgICBwbGF5ZWRHYW1lczogc3RhbmRpbmcucGxheWVkIHx8IHN0YW5kaW5nLnBsYXllZEdhbWVzIHx8IDAsXG4gICAgICAgICAgICAgICAgICAgICAgICB3aW5zOiBzdGFuZGluZy53aW4gfHwgc3RhbmRpbmcud2lucyB8fCAwLFxuICAgICAgICAgICAgICAgICAgICAgICAgZHJhd3M6IHN0YW5kaW5nLmRyYXcgfHwgc3RhbmRpbmcuZHJhd3MgfHwgMCxcbiAgICAgICAgICAgICAgICAgICAgICAgIGxvc3Nlczogc3RhbmRpbmcubG9zZSB8fCBzdGFuZGluZy5sb3NzZXMgfHwgMCxcbiAgICAgICAgICAgICAgICAgICAgICAgIGdvYWxzRm9yOiBzdGFuZGluZy5nb2Fsc0ZvciB8fCBzdGFuZGluZy5nb2Fscz8uZm9yIHx8IDAsXG4gICAgICAgICAgICAgICAgICAgICAgICBnb2Fsc0FnYWluc3Q6IHN0YW5kaW5nLmdvYWxzQWdhaW5zdCB8fCBzdGFuZGluZy5nb2Fscz8uYWdhaW5zdCB8fCAwLFxuICAgICAgICAgICAgICAgICAgICAgICAgZ29hbERpZmZlcmVuY2U6IChzdGFuZGluZy5nb2Fsc0RpZmYgIT09IHVuZGVmaW5lZClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gc3RhbmRpbmcuZ29hbHNEaWZmXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA6IChzdGFuZGluZy5nb2FsRGlmZmVyZW5jZSAhPT0gdW5kZWZpbmVkKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPyBzdGFuZGluZy5nb2FsRGlmZmVyZW5jZVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiAoc3RhbmRpbmcuZ29hbHNGb3IgfHwgMCkgLSAoc3RhbmRpbmcuZ29hbHNBZ2FpbnN0IHx8IDApLFxuICAgICAgICAgICAgICAgICAgICAgICAgZm9ybSxcbiAgICAgICAgICAgICAgICAgICAgICAgIC8vIFN0b3JlIG9yaWdpbmFsIEFQSSBmaWVsZHNcbiAgICAgICAgICAgICAgICAgICAgICAgIGV4dGVybmFsSWQ6IHN0YW5kaW5nLmV4dGVybmFsSWQgfHwgc3RhbmRpbmcuaWQsXG4gICAgICAgICAgICAgICAgICAgICAgICB0ZWFtSWQ6IHN0YW5kaW5nLnRlYW1JZCB8fCBzdGFuZGluZy50ZWFtPy5leHRlcm5hbElkLFxuICAgICAgICAgICAgICAgICAgICAgICAgdGVhbU5hbWU6IHN0YW5kaW5nLnRlYW1OYW1lIHx8IHN0YW5kaW5nLnRlYW0/Lm5hbWUsXG4gICAgICAgICAgICAgICAgICAgICAgICB0ZWFtTG9nbzogc3RhbmRpbmcudGVhbUxvZ28gfHwgc3RhbmRpbmcudGVhbT8ubG9nbyxcbiAgICAgICAgICAgICAgICAgIH07XG4gICAgICAgICAgICB9KTtcblxuICAgICAgICAgICAgY29uc3Qgc29ydGVkRGF0YSA9IHRyYW5zZm9ybWVkRGF0YS5zb3J0KChhOiBTdGFuZGluZ0RhdGEsIGI6IFN0YW5kaW5nRGF0YSkgPT4gYS5wb3NpdGlvbiAtIGIucG9zaXRpb24pO1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ+KchSBUcmFuc2Zvcm1lZCBzdGFuZGluZ3MgZGF0YTonLCBzb3J0ZWREYXRhLnNsaWNlKDAsIDMpLm1hcChzID0+ICh7IHBvc2l0aW9uOiBzLnBvc2l0aW9uLCB0ZWFtTmFtZTogcy50ZWFtLm5hbWUsIHBvaW50czogcy5wb2ludHMgfSkpKTtcbiAgICAgICAgICAgIHJldHVybiBzb3J0ZWREYXRhO1xuXG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcign4p2MIEVycm9yIGZldGNoaW5nIHN0YW5kaW5nczonLCBlcnJvcik7XG4gICAgICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgIH1cbn07XG5cbi8vIEdlbmVyYXRlIGZhbGxiYWNrIHN0YW5kaW5ncyBkYXRhIGZvciBkZXZlbG9wbWVudC90ZXN0aW5nXG5jb25zdCBnZW5lcmF0ZUZhbGxiYWNrU3RhbmRpbmdzID0gKGxlYWd1ZUlkOiBzdHJpbmcpOiBTdGFuZGluZ0RhdGFbXSA9PiB7XG4gICAgICBjb25zdCB0ZWFtcyA9IFtcbiAgICAgICAgICAgIHsgaWQ6ICczMycsIG5hbWU6ICdNYW5jaGVzdGVyIFVuaXRlZCcsIGxvZ286ICdodHRwczovL21lZGlhLmFwaS1zcG9ydHMuaW8vZm9vdGJhbGwvdGVhbXMvMzMucG5nJyB9LFxuICAgICAgICAgICAgeyBpZDogJzUwJywgbmFtZTogJ01hbmNoZXN0ZXIgQ2l0eScsIGxvZ286ICdodHRwczovL21lZGlhLmFwaS1zcG9ydHMuaW8vZm9vdGJhbGwvdGVhbXMvNTAucG5nJyB9LFxuICAgICAgICAgICAgeyBpZDogJzQyJywgbmFtZTogJ0Fyc2VuYWwnLCBsb2dvOiAnaHR0cHM6Ly9tZWRpYS5hcGktc3BvcnRzLmlvL2Zvb3RiYWxsL3RlYW1zLzQyLnBuZycgfSxcbiAgICAgICAgICAgIHsgaWQ6ICc0MCcsIG5hbWU6ICdMaXZlcnBvb2wnLCBsb2dvOiAnaHR0cHM6Ly9tZWRpYS5hcGktc3BvcnRzLmlvL2Zvb3RiYWxsL3RlYW1zLzQwLnBuZycgfSxcbiAgICAgICAgICAgIHsgaWQ6ICc0OScsIG5hbWU6ICdDaGVsc2VhJywgbG9nbzogJ2h0dHBzOi8vbWVkaWEuYXBpLXNwb3J0cy5pby9mb290YmFsbC90ZWFtcy80OS5wbmcnIH0sXG4gICAgICAgICAgICB7IGlkOiAnNDcnLCBuYW1lOiAnVG90dGVuaGFtJywgbG9nbzogJ2h0dHBzOi8vbWVkaWEuYXBpLXNwb3J0cy5pby9mb290YmFsbC90ZWFtcy80Ny5wbmcnIH0sXG4gICAgICAgICAgICB7IGlkOiAnMzQnLCBuYW1lOiAnTmV3Y2FzdGxlJywgbG9nbzogJ2h0dHBzOi8vbWVkaWEuYXBpLXNwb3J0cy5pby9mb290YmFsbC90ZWFtcy8zNC5wbmcnIH0sXG4gICAgICAgICAgICB7IGlkOiAnNjYnLCBuYW1lOiAnQXN0b24gVmlsbGEnLCBsb2dvOiAnaHR0cHM6Ly9tZWRpYS5hcGktc3BvcnRzLmlvL2Zvb3RiYWxsL3RlYW1zLzY2LnBuZycgfSxcbiAgICAgICAgICAgIHsgaWQ6ICc1MScsIG5hbWU6ICdCcmlnaHRvbicsIGxvZ286ICdodHRwczovL21lZGlhLmFwaS1zcG9ydHMuaW8vZm9vdGJhbGwvdGVhbXMvNTEucG5nJyB9LFxuICAgICAgICAgICAgeyBpZDogJzM5JywgbmFtZTogJ1dvbHZlcycsIGxvZ286ICdodHRwczovL21lZGlhLmFwaS1zcG9ydHMuaW8vZm9vdGJhbGwvdGVhbXMvMzkucG5nJyB9LFxuICAgICAgXTtcblxuICAgICAgcmV0dXJuIHRlYW1zLm1hcCgodGVhbSwgaW5kZXgpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IHBsYXllZCA9IDIwICsgTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogMTApO1xuICAgICAgICAgICAgY29uc3Qgd2lucyA9IE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIHBsYXllZCAqIDAuNyk7XG4gICAgICAgICAgICBjb25zdCBsb3NzZXMgPSBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiAocGxheWVkIC0gd2lucykgKiAwLjYpO1xuICAgICAgICAgICAgY29uc3QgZHJhd3MgPSBwbGF5ZWQgLSB3aW5zIC0gbG9zc2VzO1xuICAgICAgICAgICAgY29uc3QgZ29hbHNGb3IgPSB3aW5zICogMiArIGRyYXdzICsgTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogMTApO1xuICAgICAgICAgICAgY29uc3QgZ29hbHNBZ2FpbnN0ID0gbG9zc2VzICogMiArIE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIGdvYWxzRm9yICogMC44KTtcblxuICAgICAgICAgICAgLy8gR2VuZXJhdGUgcmVhbGlzdGljIGZvcm0gKGxhc3QgNSBtYXRjaGVzKVxuICAgICAgICAgICAgY29uc3QgZm9ybVJlc3VsdHMgPSBbJ1cnLCAnTCcsICdEJ107XG4gICAgICAgICAgICBjb25zdCBmb3JtID0gQXJyYXkuZnJvbSh7IGxlbmd0aDogNSB9LCAoKSA9PlxuICAgICAgICAgICAgICAgICAgZm9ybVJlc3VsdHNbTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogZm9ybVJlc3VsdHMubGVuZ3RoKV1cbiAgICAgICAgICAgICkgYXMgKCdXJyB8ICdMJyB8ICdEJylbXTtcblxuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICAgIHBvc2l0aW9uOiBpbmRleCArIDEsXG4gICAgICAgICAgICAgICAgICB0ZWFtLFxuICAgICAgICAgICAgICAgICAgcG9pbnRzOiB3aW5zICogMyArIGRyYXdzLFxuICAgICAgICAgICAgICAgICAgcGxheWVkR2FtZXM6IHBsYXllZCxcbiAgICAgICAgICAgICAgICAgIHdpbnMsXG4gICAgICAgICAgICAgICAgICBkcmF3cyxcbiAgICAgICAgICAgICAgICAgIGxvc3NlcyxcbiAgICAgICAgICAgICAgICAgIGdvYWxzRm9yLFxuICAgICAgICAgICAgICAgICAgZ29hbHNBZ2FpbnN0LFxuICAgICAgICAgICAgICAgICAgZ29hbERpZmZlcmVuY2U6IGdvYWxzRm9yIC0gZ29hbHNBZ2FpbnN0LFxuICAgICAgICAgICAgICAgICAgZm9ybSxcbiAgICAgICAgICAgICAgICAgIGV4dGVybmFsSWQ6IHBhcnNlSW50KHRlYW0uaWQpLFxuICAgICAgICAgICAgICAgICAgdGVhbUlkOiBwYXJzZUludCh0ZWFtLmlkKSxcbiAgICAgICAgICAgICAgICAgIHRlYW1OYW1lOiB0ZWFtLm5hbWUsXG4gICAgICAgICAgICAgICAgICB0ZWFtTG9nbzogdGVhbS5sb2dvLFxuICAgICAgICAgICAgfTtcbiAgICAgIH0pLnNvcnQoKGEsIGIpID0+IGIucG9pbnRzIC0gYS5wb2ludHMgfHwgYi5nb2FsRGlmZmVyZW5jZSAtIGEuZ29hbERpZmZlcmVuY2UpO1xufTtcblxuZXhwb3J0IGNvbnN0IHVzZUxlYWd1ZVN0YW5kaW5ncyA9ICh7XG4gICAgICBsZWFndWVJZCxcbiAgICAgIHNlYXNvbixcbiAgICAgIGZvcm1hdCA9ICdleHRlcm5hbCdcbn06IFVzZUxlYWd1ZVN0YW5kaW5nc1Byb3BzKTogVXNlTGVhZ3VlU3RhbmRpbmdzUmV0dXJuID0+IHtcbiAgICAgIGNvbnN0IFtzdGFuZGluZ3MsIHNldFN0YW5kaW5nc10gPSB1c2VTdGF0ZTxTdGFuZGluZ0RhdGFbXT4oW10pO1xuICAgICAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG4gICAgICBjb25zdCBbZXJyb3IsIHNldEVycm9yXSA9IHVzZVN0YXRlPHN0cmluZyB8IG51bGw+KG51bGwpO1xuXG4gICAgICBjb25zb2xlLmxvZygn8J+Pl++4jyB1c2VMZWFndWVTdGFuZGluZ3MgaG9vayBpbml0aWFsaXplZDonLCB7IGxlYWd1ZUlkLCBzZWFzb24sIGZvcm1hdCB9KTtcblxuICAgICAgY29uc3QgcmVmcmVzaFN0YW5kaW5ncyA9IGFzeW5jICgpID0+IHtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgICAgICAgICAgICAgICAgc2V0RXJyb3IobnVsbCk7XG4gICAgICAgICAgICAgICAgICBjb25zdCBzdGFuZGluZ3NEYXRhID0gYXdhaXQgZmV0Y2hTdGFuZGluZ3NGcm9tQVBJKGxlYWd1ZUlkLCBzZWFzb24sIGZvcm1hdCk7XG4gICAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+OryBTZXR0aW5nIHN0YW5kaW5ncyBkYXRhIGluIGhvb2s6Jywgc3RhbmRpbmdzRGF0YS5sZW5ndGgsICd0ZWFtcycpO1xuICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/Cfjq8gRmlyc3QgdGVhbSBpbiBzdGFuZGluZ3M6Jywgc3RhbmRpbmdzRGF0YVswXT8udGVhbT8ubmFtZSk7XG4gICAgICAgICAgICAgICAgICBzZXRTdGFuZGluZ3Moc3RhbmRpbmdzRGF0YSk7XG4gICAgICAgICAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCBFcnJvciBmZXRjaGluZyBzdGFuZGluZ3M6JywgZXJyKTtcbiAgICAgICAgICAgICAgICAgIHNldEVycm9yKGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAnRmFpbGVkIHRvIGZldGNoIHN0YW5kaW5ncycpO1xuICAgICAgICAgICAgICAgICAgLy8gVXNlIGZhbGxiYWNrIGRhdGEgaW4gY2FzZSBvZiBlcnJvclxuICAgICAgICAgICAgICAgICAgY29uc29sZS53YXJuKCfimqDvuI8gVXNpbmcgZmFsbGJhY2sgc3RhbmRpbmdzIGRhdGEgZHVlIHRvIGVycm9yJyk7XG4gICAgICAgICAgICAgICAgICBzZXRTdGFuZGluZ3MoZ2VuZXJhdGVGYWxsYmFja1N0YW5kaW5ncyhsZWFndWVJZCkpO1xuICAgICAgICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgICAgICAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgICAgICAgICAgfVxuICAgICAgfTtcblxuICAgICAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgICAgIGlmIChsZWFndWVJZCkge1xuICAgICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/CflIQgdXNlTGVhZ3VlU3RhbmRpbmdzIHVzZUVmZmVjdCB0cmlnZ2VyZWQ6JywgeyBsZWFndWVJZCwgc2Vhc29uLCBmb3JtYXQgfSk7XG4gICAgICAgICAgICAgICAgICAvLyBDbGVhciBhbnkgZXhpc3RpbmcgZGF0YSBmaXJzdFxuICAgICAgICAgICAgICAgICAgc2V0U3RhbmRpbmdzKFtdKTtcbiAgICAgICAgICAgICAgICAgIHNldEVycm9yKG51bGwpO1xuICAgICAgICAgICAgICAgICAgcmVmcmVzaFN0YW5kaW5ncygpO1xuICAgICAgICAgICAgfVxuICAgICAgfSwgW2xlYWd1ZUlkLCBzZWFzb24sIGZvcm1hdF0pO1xuXG4gICAgICAvLyBDYWxjdWxhdGUgc3RhdGlzdGljc1xuICAgICAgY29uc3Qgc3RhdHMgPSB1c2VNZW1vKCgpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IHRvdGFsVGVhbXMgPSBzdGFuZGluZ3MubGVuZ3RoO1xuICAgICAgICAgICAgY29uc3QgdG9wVGVhbSA9IHN0YW5kaW5ncy5sZW5ndGggPiAwID8gc3RhbmRpbmdzWzBdIDogbnVsbDtcblxuICAgICAgICAgICAgY29uc3QgdG90YWxHYW1lcyA9IHN0YW5kaW5ncy5yZWR1Y2UoKHN1bSwgdGVhbSkgPT4gc3VtICsgdGVhbS5wbGF5ZWRHYW1lcywgMCk7XG4gICAgICAgICAgICBjb25zdCB0b3RhbEdvYWxzID0gc3RhbmRpbmdzLnJlZHVjZSgoc3VtLCB0ZWFtKSA9PiBzdW0gKyB0ZWFtLmdvYWxzRm9yLCAwKTtcbiAgICAgICAgICAgIGNvbnN0IGF2Z0dvYWxzUGVyR2FtZSA9IHRvdGFsR2FtZXMgPiAwID8gTWF0aC5yb3VuZCgodG90YWxHb2FscyAvIHRvdGFsR2FtZXMpICogMTAwKSAvIDEwMCA6IDA7XG5cbiAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgICB0b3RhbFRlYW1zLFxuICAgICAgICAgICAgICAgICAgdG9wVGVhbSxcbiAgICAgICAgICAgICAgICAgIGF2Z0dvYWxzUGVyR2FtZSxcbiAgICAgICAgICAgIH07XG4gICAgICB9LCBbc3RhbmRpbmdzXSk7XG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBzdGFuZGluZ3MsXG4gICAgICAgICAgICBsb2FkaW5nLFxuICAgICAgICAgICAgZXJyb3IsXG4gICAgICAgICAgICByZWZyZXNoU3RhbmRpbmdzLFxuICAgICAgICAgICAgc3RhdHMsXG4gICAgICB9O1xufTtcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZU1lbW8iLCJmZXRjaFN0YW5kaW5nc0Zyb21BUEkiLCJsZWFndWVJZCIsInNlYXNvbiIsImZvcm1hdCIsImFwaURhdGEiLCJjdXJyZW50U2Vhc29uIiwiRGF0ZSIsImdldEZ1bGxZZWFyIiwiZm9ybWF0UGFyYW0iLCJjb25zb2xlIiwibG9nIiwidGltZXN0YW1wIiwibm93IiwiYXBpVXJsIiwicmVzcG9uc2UiLCJmZXRjaCIsIm9rIiwiRXJyb3IiLCJzdGF0dXMiLCJqc29uIiwiaGFzRGF0YSIsImRhdGEiLCJpc0FycmF5IiwiQXJyYXkiLCJkYXRhTGVuZ3RoIiwibGVuZ3RoIiwiZmlyc3RJdGVtIiwid2FybiIsImdlbmVyYXRlRmFsbGJhY2tTdGFuZGluZ3MiLCJ0cmFuc2Zvcm1lZERhdGEiLCJtYXAiLCJzdGFuZGluZyIsImluZGV4IiwiZm9ybSIsInNwbGl0Iiwic2xpY2UiLCJmb3JtUmVzdWx0cyIsImZyb20iLCJNYXRoIiwiZmxvb3IiLCJyYW5kb20iLCJwb3NpdGlvbiIsInJhbmsiLCJ0ZWFtIiwiaWQiLCJ0ZWFtSWQiLCJ0b1N0cmluZyIsImV4dGVybmFsSWQiLCJuYW1lIiwidGVhbU5hbWUiLCJsb2dvIiwidGVhbUxvZ28iLCJwb2ludHMiLCJwbGF5ZWRHYW1lcyIsInBsYXllZCIsIndpbnMiLCJ3aW4iLCJkcmF3cyIsImRyYXciLCJsb3NzZXMiLCJsb3NlIiwiZ29hbHNGb3IiLCJnb2FscyIsImZvciIsImdvYWxzQWdhaW5zdCIsImFnYWluc3QiLCJnb2FsRGlmZmVyZW5jZSIsImdvYWxzRGlmZiIsInVuZGVmaW5lZCIsInNvcnRlZERhdGEiLCJzb3J0IiwiYSIsImIiLCJzIiwiZXJyb3IiLCJ0ZWFtcyIsInBhcnNlSW50IiwidXNlTGVhZ3VlU3RhbmRpbmdzIiwic3RhbmRpbmdzIiwic2V0U3RhbmRpbmdzIiwibG9hZGluZyIsInNldExvYWRpbmciLCJzZXRFcnJvciIsInJlZnJlc2hTdGFuZGluZ3MiLCJzdGFuZGluZ3NEYXRhIiwiZXJyIiwibWVzc2FnZSIsInN0YXRzIiwidG90YWxUZWFtcyIsInRvcFRlYW0iLCJ0b3RhbEdhbWVzIiwicmVkdWNlIiwic3VtIiwidG90YWxHb2FscyIsImF2Z0dvYWxzUGVyR2FtZSIsInJvdW5kIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/hooks/leagues/useLeagueStandings.ts\n"));

/***/ })

});