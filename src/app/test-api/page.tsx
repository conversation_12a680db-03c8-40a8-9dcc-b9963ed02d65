'use client';

import { useState, useEffect } from 'react';

export default function TestAPIPage() {
  const [standingsData, setStandingsData] = useState<any>(null);
  const [teamsData, setTeamsData] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('🔄 Testing API calls...');
        
        // Test standings API
        const standingsResponse = await fetch(`/api/standings?league=340&season=2025&_t=${Date.now()}`);
        const standings = await standingsResponse.json();
        console.log('📊 Standings API response:', standings);
        setStandingsData(standings);

        // Test teams API
        const teamsResponse = await fetch(`/api/teams?league=340&season=2025&limit=50&_t=${Date.now()}`);
        const teams = await teamsResponse.json();
        console.log('👥 Teams API response:', teams);
        setTeamsData(teams);

      } catch (error) {
        console.error('❌ API test error:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return <div className="p-8">Loading API test...</div>;
  }

  return (
    <div className="p-8 space-y-8">
      <h1 className="text-2xl font-bold">API Test Page</h1>
      
      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Standings API Test</h2>
        <div className="bg-gray-100 p-4 rounded">
          <p><strong>Data count:</strong> {standingsData?.data?.length || 0}</p>
          <p><strong>First team:</strong> {standingsData?.data?.[0]?.teamName || 'N/A'}</p>
          <details>
            <summary>Raw data</summary>
            <pre className="text-xs overflow-auto">{JSON.stringify(standingsData, null, 2)}</pre>
          </details>
        </div>
      </div>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">Teams API Test</h2>
        <div className="bg-gray-100 p-4 rounded">
          <p><strong>Data count:</strong> {teamsData?.data?.length || 0}</p>
          <p><strong>First team:</strong> {teamsData?.data?.[0]?.name || 'N/A'}</p>
          <details>
            <summary>Raw data</summary>
            <pre className="text-xs overflow-auto">{JSON.stringify(teamsData, null, 2)}</pre>
          </details>
        </div>
      </div>
    </div>
  );
}
