"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/leagues/[id]/page",{

/***/ "(app-pages-browser)/./src/components/leagues/detail/TeamQuickViewModal.tsx":
/*!**************************************************************!*\
  !*** ./src/components/leagues/detail/TeamQuickViewModal.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TeamQuickViewModal: function() { return /* binding */ TeamQuickViewModal; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3Icon,CalendarIcon,ExternalLinkIcon,MapPinIcon,TrophyIcon,UsersIcon,XIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3Icon,CalendarIcon,ExternalLinkIcon,MapPinIcon,TrophyIcon,UsersIcon,XIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3Icon,CalendarIcon,ExternalLinkIcon,MapPinIcon,TrophyIcon,UsersIcon,XIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3Icon,CalendarIcon,ExternalLinkIcon,MapPinIcon,TrophyIcon,UsersIcon,XIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3Icon,CalendarIcon,ExternalLinkIcon,MapPinIcon,TrophyIcon,UsersIcon,XIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3Icon,CalendarIcon,ExternalLinkIcon,MapPinIcon,TrophyIcon,UsersIcon,XIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3Icon,CalendarIcon,ExternalLinkIcon,MapPinIcon,TrophyIcon,UsersIcon,XIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3Icon,CalendarIcon,ExternalLinkIcon,MapPinIcon,TrophyIcon,UsersIcon,XIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/external-link.js\");\n/* harmony import */ var _lib_utils_image__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils/image */ \"(app-pages-browser)/./src/lib/utils/image.ts\");\n/* __next_internal_client_entry_do_not_use__ TeamQuickViewModal,default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nconst TeamQuickViewModal = (param)=>{\n    let { team, leagueId, season, isOpen, onClose } = param;\n    var _fixtures_played, _fixtures_wins, _fixtures_draws, _fixtures_loses, _goals_for_total, _goals_for, _goals_against_total, _goals_against;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [teamStats, setTeamStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Fetch team statistics when modal opens\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!team || !isOpen) return;\n        const fetchTeamStats = async ()=>{\n            setLoading(true);\n            setError(null);\n            try {\n                console.log(\"\\uD83D\\uDD04 Fetching team statistics:\", {\n                    teamId: team.externalId,\n                    leagueId,\n                    season: season || new Date().getFullYear()\n                });\n                const response = await fetch(\"/api/teams/statistics?team=\".concat(team.externalId, \"&league=\").concat(leagueId, \"&season=\").concat(season || new Date().getFullYear()));\n                if (!response.ok) {\n                    throw new Error(\"Failed to fetch team statistics: \".concat(response.status));\n                }\n                const data = await response.json();\n                console.log(\"✅ Team statistics response:\", data);\n                setTeamStats(data.data);\n            } catch (err) {\n                console.error(\"❌ Error fetching team statistics:\", err);\n                setError(err instanceof Error ? err.message : \"Failed to load team statistics\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchTeamStats();\n    }, [\n        team,\n        leagueId,\n        season,\n        isOpen\n    ]);\n    if (!team) return null;\n    // Calculate stats from API data or fallback to basic data\n    const stats = (teamStats === null || teamStats === void 0 ? void 0 : teamStats.statistics) || {};\n    const fixtures = stats.fixtures || {};\n    const goals = stats.goals || {};\n    const matchesPlayed = ((_fixtures_played = fixtures.played) === null || _fixtures_played === void 0 ? void 0 : _fixtures_played.total) || 0;\n    const wins = ((_fixtures_wins = fixtures.wins) === null || _fixtures_wins === void 0 ? void 0 : _fixtures_wins.total) || 0;\n    const draws = ((_fixtures_draws = fixtures.draws) === null || _fixtures_draws === void 0 ? void 0 : _fixtures_draws.total) || 0;\n    const losses = ((_fixtures_loses = fixtures.loses) === null || _fixtures_loses === void 0 ? void 0 : _fixtures_loses.total) || 0;\n    const goalsFor = ((_goals_for = goals.for) === null || _goals_for === void 0 ? void 0 : (_goals_for_total = _goals_for.total) === null || _goals_for_total === void 0 ? void 0 : _goals_for_total.total) || 0;\n    const goalsAgainst = ((_goals_against = goals.against) === null || _goals_against === void 0 ? void 0 : (_goals_against_total = _goals_against.total) === null || _goals_against_total === void 0 ? void 0 : _goals_against_total.total) || 0;\n    const points = wins * 3 + draws;\n    const winRate = matchesPlayed > 0 ? Math.round(wins / matchesPlayed * 100) : 0;\n    const goalDifference = goalsFor - goalsAgainst;\n    // Handle navigation to team statistics page\n    const handleViewDetails = ()=>{\n        router.push(\"/dashboard/teams/\".concat(team.externalId, \"/statistics\"));\n        onClose();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n        open: isOpen,\n        onOpenChange: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n            className: \"max-w-2xl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                                    className: \"text-xl font-semibold\",\n                                    children: \"Team Quick View\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    onClick: onClose,\n                                    className: \"h-8 w-8\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 43\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 31\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.Avatar, {\n                                    className: \"h-16 w-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.AvatarImage, {\n                                            src: (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_9__.buildTeamLogoUrl)(team.logo),\n                                            alt: \"\".concat(team.name, \" logo\")\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_7__.AvatarFallback, {\n                                            className: \"bg-blue-500 text-white text-lg font-bold\",\n                                            children: team.name.substring(0, 2).toUpperCase()\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 43\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: team.name\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-4 text-sm text-muted-foreground mt-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                            lineNumber: 145,\n                                                            columnNumber: 55\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"Founded \",\n                                                                team.founded || \"N/A\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                            lineNumber: 146,\n                                                            columnNumber: 55\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 55\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: team.country || \"Unknown\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                            lineNumber: 150,\n                                                            columnNumber: 55\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 43\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                    variant: \"secondary\",\n                                    className: \"text-lg px-3 py-1\",\n                                    children: [\n                                        \"ID: \",\n                                        team.externalId\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 31\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 25\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: [\n                        loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 text-sm text-muted-foreground\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Loading team statistics...\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 49\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 43\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                    children: Array.from({\n                                        length: 4\n                                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-3 bg-muted/50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.Skeleton, {\n                                                    className: \"h-8 w-12 mx-auto mb-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 61\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_8__.Skeleton, {\n                                                    className: \"h-3 w-16 mx-auto\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 61\n                                                }, undefined)\n                                            ]\n                                        }, i, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 55\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 43\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 37\n                        }, undefined),\n                        error && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 p-4 bg-red-50 border border-red-200 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-5 w-5 text-red-600\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 43\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-red-800\",\n                                            children: \"Failed to load statistics\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-red-600\",\n                                            children: error\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 49\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 43\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 37\n                        }, undefined),\n                        !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 43\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-3 bg-muted/50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-blue-600\",\n                                                    children: points\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 55\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: \"Points\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 55\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-3 bg-muted/50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold text-green-600\",\n                                                    children: [\n                                                        winRate,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 55\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: \"Win Rate\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 55\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-3 bg-muted/50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold \".concat(goalDifference >= 0 ? \"text-green-600\" : \"text-red-600\"),\n                                                    children: [\n                                                        goalDifference >= 0 ? \"+\" : \"\",\n                                                        goalDifference\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 55\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: \"Goal Diff\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 55\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center p-3 bg-muted/50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: matchesPlayed\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 216,\n                                                    columnNumber: 55\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-muted-foreground\",\n                                                    children: \"Matches\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 219,\n                                                    columnNumber: 55\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 49\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 43\n                                }, undefined)\n                            ]\n                        }, void 0, true),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold mb-3 flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Recent Form (Last 5 matches)\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 43\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-2\",\n                                    children: [\n                                        team.recentForm.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold \".concat(getFormColor(result)),\n                                                children: result\n                                            }, index, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 49\n                                            }, undefined)),\n                                        team.recentForm.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-muted-foreground\",\n                                            children: \"No recent matches\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 49\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 31\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-semibold mb-3\",\n                                    children: \"Season Statistics\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 37\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-4 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"Wins:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-green-600\",\n                                                    children: team.stats.wins\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"Draws:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-yellow-600\",\n                                                    children: team.stats.draws\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"Losses:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-red-600\",\n                                                    children: team.stats.losses\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 260,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"Goals For:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: team.stats.goalsFor\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"Goals Against:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: team.stats.goalsAgainst\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 43\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"Total Points:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-blue-600\",\n                                                    children: team.stats.points\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 270,\n                                            columnNumber: 43\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 37\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 31\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_6__.Separator, {}, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 31\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\",\n                            children: [\n                                team.stadium && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-muted-foreground\",\n                                            children: \"Stadium:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: team.stadium\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        team.capacity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-muted-foreground\",\n                                            children: [\n                                                \"(\",\n                                                team.capacity.toLocaleString(),\n                                                \")\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 55\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 43\n                                }, undefined),\n                                team.manager && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-muted-foreground\",\n                                            children: \"Manager:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: team.manager\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 49\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 43\n                                }, undefined),\n                                team.playersCount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-muted-foreground\",\n                                            children: \"Squad Size:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: [\n                                                team.playersCount,\n                                                \" players\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 49\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 43\n                                }, undefined),\n                                team.website && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3Icon_CalendarIcon_ExternalLinkIcon_MapPinIcon_TrophyIcon_UsersIcon_XIcon_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 49\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: team.website,\n                                            target: \"_blank\",\n                                            rel: \"noopener noreferrer\",\n                                            className: \"text-blue-600 hover:underline font-medium\",\n                                            children: \"Official Website\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 49\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 43\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 31\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 25\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogFooter, {\n                    className: \"space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            variant: \"outline\",\n                            onClick: onClose,\n                            children: \"Close\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 322,\n                            columnNumber: 31\n                        }, undefined),\n                        onViewDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                            onClick: ()=>onViewDetails(team.id),\n                            children: \"View Full Details\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 37\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n                    lineNumber: 321,\n                    columnNumber: 25\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n            lineNumber: 114,\n            columnNumber: 19\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/home/<USER>/FECMS-sport/src/components/leagues/detail/TeamQuickViewModal.tsx\",\n        lineNumber: 113,\n        columnNumber: 13\n    }, undefined);\n};\n_s(TeamQuickViewModal, \"oWpYBsScgldL8JmbHcF6f80CA18=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = TeamQuickViewModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (TeamQuickViewModal);\nvar _c;\n$RefreshReg$(_c, \"TeamQuickViewModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/leagues/detail/TeamQuickViewModal.tsx\n"));

/***/ })

});