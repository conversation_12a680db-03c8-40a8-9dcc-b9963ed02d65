'use client';

import { useState } from 'react';
import LeagueStandings from '@/components/leagues/detail/LeagueStandings';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

export default function TestStandingsPage() {
  const [leagueId, setLeagueId] = useState('340');
  const [season, setSeason] = useState(2025);

  return (
    <div className="p-8 space-y-8">
      <h1 className="text-2xl font-bold">League Standings Test Page</h1>

      <Card>
        <CardHeader>
          <CardTitle>Test Controls</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">League ID</label>
              <Input
                value={leagueId}
                onChange={(e) => setLeagueId(e.target.value)}
                placeholder="Enter league ID"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Season</label>
              <Input
                type="number"
                value={season}
                onChange={(e) => setSeason(parseInt(e.target.value))}
                placeholder="Enter season"
              />
            </div>
          </div>
          <Button onClick={() => window.location.reload()}>
            Refresh Component
          </Button>
        </CardContent>
      </Card>

      <div className="space-y-4">
        <h2 className="text-xl font-semibold">League Standings Component</h2>
        <LeagueStandings
          key={`${leagueId}-${season}-${Date.now()}`}
          leagueId={leagueId}
          season={season}
        />
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Debug Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm">
            <p><strong>League ID:</strong> {leagueId}</p>
            <p><strong>Season:</strong> {season}</p>
            <p><strong>API URL:</strong> /api/standings?league={leagueId}&season={season}</p>
            <p><strong>Expected Teams:</strong> Nam Dinh, Ha Noi, Công An Nhân Dân...</p>
            <p><strong>Check Console:</strong> Open DevTools to see debug logs</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
