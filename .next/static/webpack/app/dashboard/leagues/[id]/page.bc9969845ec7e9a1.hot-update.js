"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/leagues/[id]/page",{

/***/ "(app-pages-browser)/./src/lib/hooks/leagues/useLeagueStandings.ts":
/*!*****************************************************!*\
  !*** ./src/lib/hooks/leagues/useLeagueStandings.ts ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLeagueStandings: function() { return /* binding */ useLeagueStandings; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useLeagueStandings auto */ \n// Fetch standings from API\nconst fetchStandingsFromAPI = async (leagueId, season, format)=>{\n    try {\n        const currentSeason = season || new Date().getFullYear();\n        const formatParam = format ? \"&format=\".concat(format) : \"\";\n        console.log(\"\\uD83D\\uDD04 Fetching standings for league:\", leagueId, \"season:\", currentSeason);\n        // Use proxy endpoint through Next.js frontend\n        const response = await fetch(\"/api/standings?league=\".concat(leagueId, \"&season=\").concat(currentSeason).concat(formatParam));\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch standings: \".concat(response.status));\n        }\n        const apiData = await response.json();\n        console.log(\"✅ Standings API response:\", apiData);\n        if (!apiData.data || !Array.isArray(apiData.data)) {\n            console.warn(\"⚠️ No standings data in API response, using fallback\");\n            return generateFallbackStandings(leagueId);\n        }\n        // Transform API data to our interface\n        return apiData.data.map((standing, index)=>{\n            var _standing_teamId, _standing_team_externalId, _standing_team, _standing_team1, _standing_team2, _standing_goals, _standing_goals1, _standing_team3, _standing_team4, _standing_team5;\n            // Parse form data from API (e.g., \"WWWWW\" -> ['W', 'W', 'W', 'W', 'W'])\n            let form = [];\n            if (standing.form && typeof standing.form === \"string\") {\n                form = standing.form.split(\"\").slice(0, 5);\n            } else {\n                // Fallback to random form if not provided\n                const formResults = [\n                    \"W\",\n                    \"L\",\n                    \"D\"\n                ];\n                form = Array.from({\n                    length: 5\n                }, ()=>formResults[Math.floor(Math.random() * formResults.length)]);\n            }\n            return {\n                position: standing.position || standing.rank || index + 1,\n                team: {\n                    id: ((_standing_teamId = standing.teamId) === null || _standing_teamId === void 0 ? void 0 : _standing_teamId.toString()) || ((_standing_team = standing.team) === null || _standing_team === void 0 ? void 0 : (_standing_team_externalId = _standing_team.externalId) === null || _standing_team_externalId === void 0 ? void 0 : _standing_team_externalId.toString()) || (index + 1).toString(),\n                    name: standing.teamName || ((_standing_team1 = standing.team) === null || _standing_team1 === void 0 ? void 0 : _standing_team1.name) || \"Team \".concat(index + 1),\n                    logo: standing.teamLogo || ((_standing_team2 = standing.team) === null || _standing_team2 === void 0 ? void 0 : _standing_team2.logo) || \"\"\n                },\n                points: standing.points || 0,\n                playedGames: standing.played || standing.playedGames || 0,\n                wins: standing.win || standing.wins || 0,\n                draws: standing.draw || standing.draws || 0,\n                losses: standing.lose || standing.losses || 0,\n                goalsFor: standing.goalsFor || ((_standing_goals = standing.goals) === null || _standing_goals === void 0 ? void 0 : _standing_goals.for) || 0,\n                goalsAgainst: standing.goalsAgainst || ((_standing_goals1 = standing.goals) === null || _standing_goals1 === void 0 ? void 0 : _standing_goals1.against) || 0,\n                goalDifference: standing.goalsDiff !== undefined ? standing.goalsDiff : standing.goalDifference !== undefined ? standing.goalDifference : (standing.goalsFor || 0) - (standing.goalsAgainst || 0),\n                form,\n                // Store original API fields\n                externalId: standing.externalId || standing.id,\n                teamId: standing.teamId || ((_standing_team3 = standing.team) === null || _standing_team3 === void 0 ? void 0 : _standing_team3.externalId),\n                teamName: standing.teamName || ((_standing_team4 = standing.team) === null || _standing_team4 === void 0 ? void 0 : _standing_team4.name),\n                teamLogo: standing.teamLogo || ((_standing_team5 = standing.team) === null || _standing_team5 === void 0 ? void 0 : _standing_team5.logo)\n            };\n        }).sort((a, b)=>a.position - b.position); // Ensure proper sorting by position\n    } catch (error) {\n        console.error(\"❌ Error fetching standings:\", error);\n        throw error;\n    }\n};\n// Generate fallback standings data for development/testing\nconst generateFallbackStandings = (leagueId)=>{\n    const teams = [\n        {\n            id: \"33\",\n            name: \"Manchester United\",\n            logo: \"https://media.api-sports.io/football/teams/33.png\"\n        },\n        {\n            id: \"50\",\n            name: \"Manchester City\",\n            logo: \"https://media.api-sports.io/football/teams/50.png\"\n        },\n        {\n            id: \"42\",\n            name: \"Arsenal\",\n            logo: \"https://media.api-sports.io/football/teams/42.png\"\n        },\n        {\n            id: \"40\",\n            name: \"Liverpool\",\n            logo: \"https://media.api-sports.io/football/teams/40.png\"\n        },\n        {\n            id: \"49\",\n            name: \"Chelsea\",\n            logo: \"https://media.api-sports.io/football/teams/49.png\"\n        },\n        {\n            id: \"47\",\n            name: \"Tottenham\",\n            logo: \"https://media.api-sports.io/football/teams/47.png\"\n        },\n        {\n            id: \"34\",\n            name: \"Newcastle\",\n            logo: \"https://media.api-sports.io/football/teams/34.png\"\n        },\n        {\n            id: \"66\",\n            name: \"Aston Villa\",\n            logo: \"https://media.api-sports.io/football/teams/66.png\"\n        },\n        {\n            id: \"51\",\n            name: \"Brighton\",\n            logo: \"https://media.api-sports.io/football/teams/51.png\"\n        },\n        {\n            id: \"39\",\n            name: \"Wolves\",\n            logo: \"https://media.api-sports.io/football/teams/39.png\"\n        }\n    ];\n    return teams.map((team, index)=>{\n        const played = 20 + Math.floor(Math.random() * 10);\n        const wins = Math.floor(Math.random() * played * 0.7);\n        const losses = Math.floor(Math.random() * (played - wins) * 0.6);\n        const draws = played - wins - losses;\n        const goalsFor = wins * 2 + draws + Math.floor(Math.random() * 10);\n        const goalsAgainst = losses * 2 + Math.floor(Math.random() * goalsFor * 0.8);\n        // Generate realistic form (last 5 matches)\n        const formResults = [\n            \"W\",\n            \"L\",\n            \"D\"\n        ];\n        const form = Array.from({\n            length: 5\n        }, ()=>formResults[Math.floor(Math.random() * formResults.length)]);\n        return {\n            position: index + 1,\n            team,\n            points: wins * 3 + draws,\n            playedGames: played,\n            wins,\n            draws,\n            losses,\n            goalsFor,\n            goalsAgainst,\n            goalDifference: goalsFor - goalsAgainst,\n            form,\n            externalId: parseInt(team.id),\n            teamId: parseInt(team.id),\n            teamName: team.name,\n            teamLogo: team.logo\n        };\n    }).sort((a, b)=>b.points - a.points || b.goalDifference - a.goalDifference);\n};\nconst useLeagueStandings = (param)=>{\n    let { leagueId, season, format = \"external\" } = param;\n    const [standings, setStandings] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const refreshStandings = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const standingsData = await fetchStandingsFromAPI(leagueId, season, format);\n            setStandings(standingsData);\n        } catch (err) {\n            console.error(\"Error fetching standings:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to fetch standings\");\n            // Use fallback data in case of error\n            setStandings(generateFallbackStandings(leagueId));\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (leagueId) {\n            refreshStandings();\n        }\n    }, [\n        leagueId,\n        season,\n        format\n    ]);\n    // Calculate statistics\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const totalTeams = standings.length;\n        const topTeam = standings.length > 0 ? standings[0] : null;\n        const totalGames = standings.reduce((sum, team)=>sum + team.playedGames, 0);\n        const totalGoals = standings.reduce((sum, team)=>sum + team.goalsFor, 0);\n        const avgGoalsPerGame = totalGames > 0 ? Math.round(totalGoals / totalGames * 100) / 100 : 0;\n        return {\n            totalTeams,\n            topTeam,\n            avgGoalsPerGame\n        };\n    }, [\n        standings\n    ]);\n    return {\n        standings,\n        loading,\n        error,\n        refreshStandings,\n        stats\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/hooks/leagues/useLeagueStandings.ts\n"));

/***/ })

});