'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export default function DebugStandingsPage() {
  const [apiResponse, setApiResponse] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const testAPI = async () => {
    setLoading(true);
    setError(null);
    setApiResponse(null);

    try {
      console.log('🔄 Testing API call...');
      
      const apiUrl = `/api/standings?league=340&season=2025&format=external&_t=${Date.now()}`;
      console.log('🌐 API URL:', apiUrl);
      
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });

      console.log('📡 Response status:', response.status, response.statusText);
      console.log('📡 Response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        throw new Error(`API Error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('✅ API Response:', data);
      
      setApiResponse(data);

      // Validate data structure
      console.log('🔍 Data validation:', {
        hasData: !!data.data,
        isArray: Array.isArray(data.data),
        dataLength: data.data?.length,
        firstItem: data.data?.[0],
        firstTeamName: data.data?.[0]?.teamName
      });

    } catch (err) {
      console.error('❌ API Test Error:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    testAPI();
  }, []);

  return (
    <div className="p-8 space-y-8">
      <h1 className="text-2xl font-bold">Debug Standings API</h1>
      
      <Card>
        <CardHeader>
          <CardTitle>API Test Controls</CardTitle>
        </CardHeader>
        <CardContent>
          <Button onClick={testAPI} disabled={loading}>
            {loading ? 'Testing...' : 'Test API Call'}
          </Button>
        </CardContent>
      </Card>

      {error && (
        <Card className="border-red-200">
          <CardHeader>
            <CardTitle className="text-red-600">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-600">{error}</p>
          </CardContent>
        </Card>
      )}

      {apiResponse && (
        <Card>
          <CardHeader>
            <CardTitle>API Response Analysis</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <strong>Has Data:</strong> {apiResponse.data ? '✅ Yes' : '❌ No'}
              </div>
              <div>
                <strong>Is Array:</strong> {Array.isArray(apiResponse.data) ? '✅ Yes' : '❌ No'}
              </div>
              <div>
                <strong>Data Length:</strong> {apiResponse.data?.length || 0}
              </div>
              <div>
                <strong>First Team:</strong> {apiResponse.data?.[0]?.teamName || 'N/A'}
              </div>
            </div>

            <div>
              <strong>First 3 Teams:</strong>
              <ul className="list-disc list-inside mt-2">
                {apiResponse.data?.slice(0, 3).map((team: any, index: number) => (
                  <li key={index}>
                    #{team.position} {team.teamName} - {team.points} pts
                  </li>
                ))}
              </ul>
            </div>

            <details>
              <summary className="cursor-pointer font-medium">Raw Response</summary>
              <pre className="text-xs bg-gray-100 p-4 rounded mt-2 overflow-auto">
                {JSON.stringify(apiResponse, null, 2)}
              </pre>
            </details>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Expected vs Actual</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-green-600">Expected (Correct)</h4>
              <ul className="text-sm space-y-1">
                <li>#1 Nam Dinh - 54 pts</li>
                <li>#2 Ha Noi - 46 pts</li>
                <li>#3 Công An Nhân Dân - 42 pts</li>
                <li>Total: 14 teams</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-red-600">Fallback (Wrong)</h4>
              <ul className="text-sm space-y-1">
                <li>#1 Manchester United</li>
                <li>#2 Manchester City</li>
                <li>#3 Arsenal</li>
                <li>Total: 10 teams</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
