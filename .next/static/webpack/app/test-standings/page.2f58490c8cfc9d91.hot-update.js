"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-standings/page",{

/***/ "(app-pages-browser)/./src/lib/hooks/leagues/useLeagueStandings.ts":
/*!*****************************************************!*\
  !*** ./src/lib/hooks/leagues/useLeagueStandings.ts ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLeagueStandings: function() { return /* binding */ useLeagueStandings; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useLeagueStandings auto */ \n// Fetch standings from API\nconst fetchStandingsFromAPI = async (leagueId, season, format)=>{\n    try {\n        var _apiData_data, _apiData_data1;\n        const currentSeason = season || new Date().getFullYear();\n        const formatParam = format ? \"&format=\".concat(format) : \"\";\n        console.log(\"\\uD83D\\uDD04 Fetching standings for league:\", leagueId, \"season:\", currentSeason);\n        // Use proxy endpoint through Next.js frontend with cache busting\n        const timestamp = Date.now();\n        const apiUrl = \"/api/standings?league=\".concat(leagueId, \"&season=\").concat(currentSeason).concat(formatParam, \"&_t=\").concat(timestamp);\n        console.log(\"\\uD83C\\uDF10 Making API call to:\", apiUrl);\n        const response = await fetch(apiUrl, {\n            method: \"GET\",\n            headers: {\n                \"Cache-Control\": \"no-cache, no-store, must-revalidate\",\n                \"Pragma\": \"no-cache\",\n                \"Expires\": \"0\"\n            }\n        });\n        if (!response.ok) {\n            console.error(\"❌ API Response not OK:\", response.status, response.statusText);\n            throw new Error(\"Failed to fetch standings: \".concat(response.status, \" \").concat(response.statusText));\n        }\n        const apiData = await response.json();\n        console.log(\"✅ Standings API response:\", apiData);\n        console.log(\"\\uD83D\\uDD0D API data structure check:\", {\n            hasData: !!apiData.data,\n            isArray: Array.isArray(apiData.data),\n            dataLength: (_apiData_data = apiData.data) === null || _apiData_data === void 0 ? void 0 : _apiData_data.length,\n            firstItem: (_apiData_data1 = apiData.data) === null || _apiData_data1 === void 0 ? void 0 : _apiData_data1[0]\n        });\n        if (!apiData.data || !Array.isArray(apiData.data)) {\n            console.warn(\"⚠️ No standings data in API response, using fallback\");\n            return generateFallbackStandings(leagueId);\n        }\n        // Transform API data to our interface\n        console.log(\"\\uD83D\\uDD04 Transforming API data to interface, count:\", apiData.data.length);\n        const transformedData = apiData.data.map((standing, index)=>{\n            var _standing_teamId, _standing_team_externalId, _standing_team, _standing_team1, _standing_team2, _standing_goals, _standing_goals1, _standing_team3, _standing_team4, _standing_team5;\n            // Parse form data from API (e.g., \"WWWWW\" -> ['W', 'W', 'W', 'W', 'W'])\n            let form = [];\n            if (standing.form && typeof standing.form === \"string\") {\n                form = standing.form.split(\"\").slice(0, 5);\n            } else {\n                // Fallback to random form if not provided\n                const formResults = [\n                    \"W\",\n                    \"L\",\n                    \"D\"\n                ];\n                form = Array.from({\n                    length: 5\n                }, ()=>formResults[Math.floor(Math.random() * formResults.length)]);\n            }\n            return {\n                position: standing.position || standing.rank || index + 1,\n                team: {\n                    id: ((_standing_teamId = standing.teamId) === null || _standing_teamId === void 0 ? void 0 : _standing_teamId.toString()) || ((_standing_team = standing.team) === null || _standing_team === void 0 ? void 0 : (_standing_team_externalId = _standing_team.externalId) === null || _standing_team_externalId === void 0 ? void 0 : _standing_team_externalId.toString()) || (index + 1).toString(),\n                    name: standing.teamName || ((_standing_team1 = standing.team) === null || _standing_team1 === void 0 ? void 0 : _standing_team1.name) || \"Team \".concat(index + 1),\n                    logo: standing.teamLogo || ((_standing_team2 = standing.team) === null || _standing_team2 === void 0 ? void 0 : _standing_team2.logo) || \"\"\n                },\n                points: standing.points || 0,\n                playedGames: standing.played || standing.playedGames || 0,\n                wins: standing.win || standing.wins || 0,\n                draws: standing.draw || standing.draws || 0,\n                losses: standing.lose || standing.losses || 0,\n                goalsFor: standing.goalsFor || ((_standing_goals = standing.goals) === null || _standing_goals === void 0 ? void 0 : _standing_goals.for) || 0,\n                goalsAgainst: standing.goalsAgainst || ((_standing_goals1 = standing.goals) === null || _standing_goals1 === void 0 ? void 0 : _standing_goals1.against) || 0,\n                goalDifference: standing.goalsDiff !== undefined ? standing.goalsDiff : standing.goalDifference !== undefined ? standing.goalDifference : (standing.goalsFor || 0) - (standing.goalsAgainst || 0),\n                form,\n                // Store original API fields\n                externalId: standing.externalId || standing.id,\n                teamId: standing.teamId || ((_standing_team3 = standing.team) === null || _standing_team3 === void 0 ? void 0 : _standing_team3.externalId),\n                teamName: standing.teamName || ((_standing_team4 = standing.team) === null || _standing_team4 === void 0 ? void 0 : _standing_team4.name),\n                teamLogo: standing.teamLogo || ((_standing_team5 = standing.team) === null || _standing_team5 === void 0 ? void 0 : _standing_team5.logo)\n            };\n        });\n        const sortedData = transformedData.sort((a, b)=>a.position - b.position);\n        console.log(\"✅ Transformed standings data:\", sortedData.slice(0, 3).map((s)=>({\n                position: s.position,\n                teamName: s.team.name,\n                points: s.points\n            })));\n        return sortedData;\n    } catch (error) {\n        console.error(\"❌ Error fetching standings:\", error);\n        throw error;\n    }\n};\n// Generate fallback standings data for development/testing\nconst generateFallbackStandings = (leagueId)=>{\n    const teams = [\n        {\n            id: \"33\",\n            name: \"Manchester United\",\n            logo: \"https://media.api-sports.io/football/teams/33.png\"\n        },\n        {\n            id: \"50\",\n            name: \"Manchester City\",\n            logo: \"https://media.api-sports.io/football/teams/50.png\"\n        },\n        {\n            id: \"42\",\n            name: \"Arsenal\",\n            logo: \"https://media.api-sports.io/football/teams/42.png\"\n        },\n        {\n            id: \"40\",\n            name: \"Liverpool\",\n            logo: \"https://media.api-sports.io/football/teams/40.png\"\n        },\n        {\n            id: \"49\",\n            name: \"Chelsea\",\n            logo: \"https://media.api-sports.io/football/teams/49.png\"\n        },\n        {\n            id: \"47\",\n            name: \"Tottenham\",\n            logo: \"https://media.api-sports.io/football/teams/47.png\"\n        },\n        {\n            id: \"34\",\n            name: \"Newcastle\",\n            logo: \"https://media.api-sports.io/football/teams/34.png\"\n        },\n        {\n            id: \"66\",\n            name: \"Aston Villa\",\n            logo: \"https://media.api-sports.io/football/teams/66.png\"\n        },\n        {\n            id: \"51\",\n            name: \"Brighton\",\n            logo: \"https://media.api-sports.io/football/teams/51.png\"\n        },\n        {\n            id: \"39\",\n            name: \"Wolves\",\n            logo: \"https://media.api-sports.io/football/teams/39.png\"\n        }\n    ];\n    return teams.map((team, index)=>{\n        const played = 20 + Math.floor(Math.random() * 10);\n        const wins = Math.floor(Math.random() * played * 0.7);\n        const losses = Math.floor(Math.random() * (played - wins) * 0.6);\n        const draws = played - wins - losses;\n        const goalsFor = wins * 2 + draws + Math.floor(Math.random() * 10);\n        const goalsAgainst = losses * 2 + Math.floor(Math.random() * goalsFor * 0.8);\n        // Generate realistic form (last 5 matches)\n        const formResults = [\n            \"W\",\n            \"L\",\n            \"D\"\n        ];\n        const form = Array.from({\n            length: 5\n        }, ()=>formResults[Math.floor(Math.random() * formResults.length)]);\n        return {\n            position: index + 1,\n            team,\n            points: wins * 3 + draws,\n            playedGames: played,\n            wins,\n            draws,\n            losses,\n            goalsFor,\n            goalsAgainst,\n            goalDifference: goalsFor - goalsAgainst,\n            form,\n            externalId: parseInt(team.id),\n            teamId: parseInt(team.id),\n            teamName: team.name,\n            teamLogo: team.logo\n        };\n    }).sort((a, b)=>b.points - a.points || b.goalDifference - a.goalDifference);\n};\nconst useLeagueStandings = (param)=>{\n    let { leagueId, season, format = \"external\" } = param;\n    const [standings, setStandings] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    console.log(\"\\uD83C\\uDFD7️ useLeagueStandings hook initialized:\", {\n        leagueId,\n        season,\n        format\n    });\n    const refreshStandings = async ()=>{\n        try {\n            var _standingsData__team, _standingsData_;\n            setLoading(true);\n            setError(null);\n            const standingsData = await fetchStandingsFromAPI(leagueId, season, format);\n            console.log(\"\\uD83C\\uDFAF Setting standings data in hook:\", standingsData.length, \"teams\");\n            console.log(\"\\uD83C\\uDFAF First team in standings:\", (_standingsData_ = standingsData[0]) === null || _standingsData_ === void 0 ? void 0 : (_standingsData__team = _standingsData_.team) === null || _standingsData__team === void 0 ? void 0 : _standingsData__team.name);\n            setStandings(standingsData);\n        } catch (err) {\n            console.error(\"❌ Error fetching standings:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to fetch standings\");\n            // Use fallback data in case of error\n            console.warn(\"⚠️ Using fallback standings data due to error\");\n            setStandings(generateFallbackStandings(leagueId));\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (leagueId) {\n            console.log(\"\\uD83D\\uDD04 useLeagueStandings useEffect triggered:\", {\n                leagueId,\n                season,\n                format\n            });\n            // Clear any existing data first\n            setStandings([]);\n            setError(null);\n            refreshStandings();\n        }\n    }, [\n        leagueId,\n        season,\n        format\n    ]);\n    // Calculate statistics\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const totalTeams = standings.length;\n        const topTeam = standings.length > 0 ? standings[0] : null;\n        const totalGames = standings.reduce((sum, team)=>sum + team.playedGames, 0);\n        const totalGoals = standings.reduce((sum, team)=>sum + team.goalsFor, 0);\n        const avgGoalsPerGame = totalGames > 0 ? Math.round(totalGoals / totalGames * 100) / 100 : 0;\n        return {\n            totalTeams,\n            topTeam,\n            avgGoalsPerGame\n        };\n    }, [\n        standings\n    ]);\n    return {\n        standings,\n        loading,\n        error,\n        refreshStandings,\n        stats\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/hooks/leagues/useLeagueStandings.ts\n"));

/***/ })

});