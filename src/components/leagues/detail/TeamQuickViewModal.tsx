
'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import {
      <PERSON>alog,
      DialogContent,
      DialogHeader,
      DialogTitle,
      DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Skeleton } from '@/components/ui/skeleton';
import {
      CalendarIcon,
      MapPinIcon,
      UsersIcon,
      TrophyIcon,
      BarChart3Icon,
      ExternalLinkIcon,
      XIcon,
      AlertTriangle,
} from 'lucide-react';
import { buildTeamLogoUrl } from '@/lib/utils/image';

interface TeamQuickViewModalProps {
      team: any; // Team data from league teams API
      leagueId: string;
      season?: number;
      isOpen: boolean;
      onClose: () => void;
}

export const TeamQuickViewModal: React.FC<TeamQuickViewModalProps> = ({
      team,
      leagueId,
      season,
      isOpen,
      onClose,
}) => {
      const router = useRouter();
      const [teamStats, setTeamStats] = useState<any>(null);
      const [loading, setLoading] = useState(false);
      const [error, setError] = useState<string | null>(null);

      // Fetch team statistics when modal opens
      useEffect(() => {
            if (!team || !isOpen) return;

            const fetchTeamStats = async () => {
                  setLoading(true);
                  setError(null);

                  try {
                        console.log('🔄 Fetching team statistics:', {
                              teamId: team.externalId,
                              leagueId,
                              season: season || new Date().getFullYear()
                        });

                        const response = await fetch(
                              `/api/teams/statistics?team=${team.externalId}&league=${leagueId}&season=${season || new Date().getFullYear()}`
                        );

                        if (!response.ok) {
                              throw new Error(`Failed to fetch team statistics: ${response.status}`);
                        }

                        const data = await response.json();
                        console.log('✅ Team statistics response:', data);
                        setTeamStats(data.data);

                  } catch (err) {
                        console.error('❌ Error fetching team statistics:', err);
                        setError(err instanceof Error ? err.message : 'Failed to load team statistics');
                  } finally {
                        setLoading(false);
                  }
            };

            fetchTeamStats();
      }, [team, leagueId, season, isOpen]);

      if (!team) return null;

      // Calculate stats from API data or fallback to basic data
      const stats = teamStats?.statistics || {};

      const matchesPlayed = stats.played?.total || 0;
      const wins = stats.wins?.total || 0;
      const draws = stats.draws?.total || 0;
      const losses = stats.loses?.total || 0;
      const goalsFor = stats.goals?.for?.total?.total || 0;
      const goalsAgainst = stats.goals?.against?.total?.total || 0;
      const points = wins * 3 + draws;

      const winRate = matchesPlayed > 0 ? Math.round((wins / matchesPlayed) * 100) : 0;
      const goalDifference = goalsFor - goalsAgainst;

      // Handle navigation to team statistics page
      const handleViewDetails = () => {
            router.push(`/dashboard/teams/${team.externalId}/statistics`);
            onClose();
      };

      return (
            <Dialog open={isOpen} onOpenChange={onClose}>
                  <DialogContent className="max-w-2xl">
                        <DialogHeader className="space-y-4">
                              <div className="flex items-center justify-between">
                                    <DialogTitle className="text-xl font-semibold">
                                          Team Quick View
                                    </DialogTitle>
                                    <Button
                                          variant="ghost"
                                          size="icon"
                                          onClick={onClose}
                                          className="h-8 w-8"
                                    >
                                          <XIcon className="h-4 w-4" />
                                    </Button>
                              </div>

                              {/* Team Header */}
                              <div className="flex items-center space-x-4">
                                    <Avatar className="h-16 w-16">
                                          <AvatarImage
                                                src={buildTeamLogoUrl(team.logo)}
                                                alt={`${team.name} logo`}
                                          />
                                          <AvatarFallback className="bg-blue-500 text-white text-lg font-bold">
                                                {team.name.substring(0, 2).toUpperCase()}
                                          </AvatarFallback>
                                    </Avatar>
                                    <div className="flex-1">
                                          <h2 className="text-2xl font-bold">{team.name}</h2>
                                          <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                                                <div className="flex items-center space-x-1">
                                                      <CalendarIcon className="h-4 w-4" />
                                                      <span>Founded {team.founded || 'N/A'}</span>
                                                </div>
                                                <div className="flex items-center space-x-1">
                                                      <MapPinIcon className="h-4 w-4" />
                                                      <span>{team.country || 'Unknown'}</span>
                                                </div>
                                          </div>
                                    </div>
                                    <Badge variant="secondary" className="text-lg px-3 py-1">
                                          ID: {team.externalId}
                                    </Badge>
                              </div>
                        </DialogHeader>

                        <div className="space-y-6">
                              {/* Loading State */}
                              {loading && (
                                    <div className="space-y-4">
                                          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                                                <span>Loading team statistics...</span>
                                          </div>
                                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                                {Array.from({ length: 4 }).map((_, i) => (
                                                      <div key={i} className="text-center p-3 bg-muted/50 rounded-lg">
                                                            <Skeleton className="h-8 w-12 mx-auto mb-2" />
                                                            <Skeleton className="h-3 w-16 mx-auto" />
                                                      </div>
                                                ))}
                                          </div>
                                    </div>
                              )}

                              {/* Error State */}
                              {error && !loading && (
                                    <div className="flex items-center space-x-2 p-4 bg-red-50 border border-red-200 rounded-lg">
                                          <AlertTriangle className="h-5 w-5 text-red-600" />
                                          <div>
                                                <p className="text-sm font-medium text-red-800">Failed to load statistics</p>
                                                <p className="text-xs text-red-600">{error}</p>
                                          </div>
                                    </div>
                              )}

                              {/* Statistics Content */}
                              {!loading && !error && (
                                    <>
                                          <Separator />

                                          {/* Quick Stats Grid */}
                                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                                <div className="text-center p-3 bg-muted/50 rounded-lg">
                                                      <div className="text-2xl font-bold text-blue-600">
                                                            {points}
                                                      </div>
                                                      <div className="text-xs text-muted-foreground">Points</div>
                                                </div>
                                                <div className="text-center p-3 bg-muted/50 rounded-lg">
                                                      <div className="text-2xl font-bold text-green-600">
                                                            {winRate}%
                                                      </div>
                                                      <div className="text-xs text-muted-foreground">Win Rate</div>
                                                </div>
                                                <div className="text-center p-3 bg-muted/50 rounded-lg">
                                                      <div className={`text-2xl font-bold ${goalDifference >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                                            {goalDifference >= 0 ? '+' : ''}{goalDifference}
                                                      </div>
                                                      <div className="text-xs text-muted-foreground">Goal Diff</div>
                                                </div>
                                                <div className="text-center p-3 bg-muted/50 rounded-lg">
                                                      <div className="text-2xl font-bold">
                                                            {matchesPlayed}
                                                      </div>
                                                      <div className="text-xs text-muted-foreground">Matches</div>
                                                </div>
                                          </div>
                                    </>
                              )}

                              {/* Detailed Stats */}
                              {!loading && !error && (
                                    <div>
                                          <h4 className="font-semibold mb-3">Season Statistics</h4>
                                          <div className="grid grid-cols-3 gap-4 text-sm">
                                                <div className="flex justify-between">
                                                      <span className="text-muted-foreground">Wins:</span>
                                                      <span className="font-medium text-green-600">{wins}</span>
                                                </div>
                                                <div className="flex justify-between">
                                                      <span className="text-muted-foreground">Draws:</span>
                                                      <span className="font-medium text-yellow-600">{draws}</span>
                                                </div>
                                                <div className="flex justify-between">
                                                      <span className="text-muted-foreground">Losses:</span>
                                                      <span className="font-medium text-red-600">{losses}</span>
                                                </div>
                                                <div className="flex justify-between">
                                                      <span className="text-muted-foreground">Goals For:</span>
                                                      <span className="font-medium">{goalsFor}</span>
                                                </div>
                                                <div className="flex justify-between">
                                                      <span className="text-muted-foreground">Goals Against:</span>
                                                      <span className="font-medium">{goalsAgainst}</span>
                                                </div>
                                                <div className="flex justify-between">
                                                      <span className="text-muted-foreground">Total Points:</span>
                                                      <span className="font-bold text-blue-600">{points}</span>
                                                </div>
                                          </div>
                                    </div>
                              )}

                              <Separator />

                              {/* Additional Info */}
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                    {teamStats?.team?.venue?.name && (
                                          <div className="flex items-center space-x-2">
                                                <TrophyIcon className="h-4 w-4 text-muted-foreground" />
                                                <span className="text-muted-foreground">Stadium:</span>
                                                <span className="font-medium">{teamStats.team.venue.name}</span>
                                                {teamStats.team.venue.capacity && (
                                                      <span className="text-muted-foreground">({teamStats.team.venue.capacity.toLocaleString()})</span>
                                                )}
                                          </div>
                                    )}
                                    <div className="flex items-center space-x-2">
                                          <MapPinIcon className="h-4 w-4 text-muted-foreground" />
                                          <span className="text-muted-foreground">League:</span>
                                          <span className="font-medium">League {leagueId}</span>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                          <CalendarIcon className="h-4 w-4 text-muted-foreground" />
                                          <span className="text-muted-foreground">Season:</span>
                                          <span className="font-medium">{season || new Date().getFullYear()}</span>
                                    </div>
                              </div>
                        </div>

                        <DialogFooter className="space-x-2">
                              <Button variant="outline" onClick={onClose}>
                                    Close
                              </Button>
                              <Button onClick={handleViewDetails} className="bg-blue-600 hover:bg-blue-700">
                                    <BarChart3Icon className="w-4 h-4 mr-2" />
                                    View Full Statistics
                              </Button>
                        </DialogFooter>
                  </DialogContent>
            </Dialog>
      );
};

export default TeamQuickViewModal;
