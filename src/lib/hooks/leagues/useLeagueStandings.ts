'use client';

import { useState, useEffect, useMemo } from 'react';

export interface StandingData {
      position: number;
      team: {
            id: string;
            name: string;
            logo?: string;
      };
      points: number;
      playedGames: number;
      wins: number;
      draws: number;
      losses: number;
      goalsFor: number;
      goalsAgainst: number;
      goalDifference: number;
      form: ('W' | 'L' | 'D')[];
      // API fields
      externalId?: number;
      teamId?: number;
      teamName?: string;
      teamLogo?: string;
}

export interface UseLeagueStandingsProps {
      leagueId: string;
      season?: number;
      format?: 'external' | 'internal';
}

export interface UseLeagueStandingsReturn {
      standings: StandingData[];
      loading: boolean;
      error: string | null;
      refreshStandings: () => void;
      stats: {
            totalTeams: number;
            topTeam: StandingData | null;
            avgGoalsPerGame: number;
      };
}

// Fetch standings from API
const fetchStandingsFromAPI = async (leagueId: string, season?: number, format?: string): Promise<StandingData[]> => {
      try {
            const currentSeason = season || new Date().getFullYear();
            const formatParam = format ? `&format=${format}` : '';
            console.log('🔄 Fetching standings for league:', leagueId, 'season:', currentSeason);

            // Use proxy endpoint through Next.js frontend with cache busting
            const timestamp = Date.now();
            const apiUrl = `/api/standings?league=${leagueId}&season=${currentSeason}${formatParam}&_t=${timestamp}`;
            console.log('🌐 Making API call to:', apiUrl);
            const response = await fetch(apiUrl, {
                  method: 'GET',
                  headers: {
                        'Cache-Control': 'no-cache, no-store, must-revalidate',
                        'Pragma': 'no-cache',
                        'Expires': '0'
                  }
            });

            if (!response.ok) {
                  console.error('❌ API Response not OK:', response.status, response.statusText);
                  throw new Error(`Failed to fetch standings: ${response.status} ${response.statusText}`);
            }

            const apiData = await response.json();
            console.log('✅ Standings API response:', apiData);

            // Extract standings data from the correct path: response[0].league.standings[0]
            let standingsArray: any[] = [];

            if (apiData.response && Array.isArray(apiData.response) && apiData.response.length > 0) {
                  const leagueData = apiData.response[0];
                  if (leagueData.league && leagueData.league.standings && Array.isArray(leagueData.league.standings)) {
                        standingsArray = leagueData.league.standings[0] || [];
                  }
            }

            console.log('🔍 Extracted standings data:', {
                  hasResponse: !!apiData.response,
                  responseLength: apiData.response?.length,
                  hasLeague: !!apiData.response?.[0]?.league,
                  hasStandings: !!apiData.response?.[0]?.league?.standings,
                  standingsLength: standingsArray.length,
                  firstTeam: standingsArray[0]?.team?.name
            });

            if (!standingsArray || !Array.isArray(standingsArray) || standingsArray.length === 0) {
                  console.error('❌ No standings data found in API response:', {
                        responseStructure: Object.keys(apiData),
                        standingsArray: standingsArray
                  });

                  // In development, throw error instead of using fallback
                  if (process.env.NODE_ENV === 'development') {
                        throw new Error(`No standings data found. Response structure: ${Object.keys(apiData).join(', ')}`);
                  }

                  console.warn('⚠️ No standings data in API response, using fallback');
                  return generateFallbackStandings(leagueId);
            }

            // Transform API data to our interface
            console.log('🔄 Transforming API data to interface, count:', standingsArray.length);
            const transformedData = standingsArray.map((standing: any, index: number) => {
                  // Parse form data from API (e.g., "WWWWW" -> ['W', 'W', 'W', 'W', 'W'])
                  let form: ('W' | 'L' | 'D')[] = [];
                  if (standing.form && typeof standing.form === 'string') {
                        form = standing.form.split('').slice(0, 5) as ('W' | 'L' | 'D')[];
                  } else {
                        // Fallback to random form if not provided
                        const formResults = ['W', 'L', 'D'];
                        form = Array.from({ length: 5 }, () =>
                              formResults[Math.floor(Math.random() * formResults.length)]
                        ) as ('W' | 'L' | 'D')[];
                  }

                  return {
                        position: standing.rank || standing.position || (index + 1),
                        team: {
                              id: standing.team?.id?.toString() || (index + 1).toString(),
                              name: standing.team?.name || `Team ${index + 1}`,
                              logo: standing.team?.logo || '',
                        },
                        points: standing.points || 0,
                        playedGames: standing.all?.played || 0,
                        wins: standing.all?.win || 0,
                        draws: standing.all?.draw || 0,
                        losses: standing.all?.lose || 0,
                        goalsFor: standing.all?.goals?.for || 0,
                        goalsAgainst: standing.all?.goals?.against || 0,
                        goalDifference: standing.goalsDiff || 0,
                        form,
                        // Store original API fields for reference
                        externalId: standing.team?.id,
                        teamId: standing.team?.id,
                        teamName: standing.team?.name,
                        teamLogo: standing.team?.logo,
                  };
            });

            const sortedData = transformedData.sort((a: StandingData, b: StandingData) => a.position - b.position);
            console.log('✅ Transformed standings data:', sortedData.slice(0, 3).map(s => ({ position: s.position, teamName: s.team.name, points: s.points })));
            return sortedData;

      } catch (error) {
            console.error('❌ Error fetching standings:', error);
            throw error;
      }
};

// Generate fallback standings data for development/testing
const generateFallbackStandings = (leagueId: string): StandingData[] => {
      const teams = [
            { id: '33', name: 'Manchester United', logo: 'https://media.api-sports.io/football/teams/33.png' },
            { id: '50', name: 'Manchester City', logo: 'https://media.api-sports.io/football/teams/50.png' },
            { id: '42', name: 'Arsenal', logo: 'https://media.api-sports.io/football/teams/42.png' },
            { id: '40', name: 'Liverpool', logo: 'https://media.api-sports.io/football/teams/40.png' },
            { id: '49', name: 'Chelsea', logo: 'https://media.api-sports.io/football/teams/49.png' },
            { id: '47', name: 'Tottenham', logo: 'https://media.api-sports.io/football/teams/47.png' },
            { id: '34', name: 'Newcastle', logo: 'https://media.api-sports.io/football/teams/34.png' },
            { id: '66', name: 'Aston Villa', logo: 'https://media.api-sports.io/football/teams/66.png' },
            { id: '51', name: 'Brighton', logo: 'https://media.api-sports.io/football/teams/51.png' },
            { id: '39', name: 'Wolves', logo: 'https://media.api-sports.io/football/teams/39.png' },
      ];

      return teams.map((team, index) => {
            const played = 20 + Math.floor(Math.random() * 10);
            const wins = Math.floor(Math.random() * played * 0.7);
            const losses = Math.floor(Math.random() * (played - wins) * 0.6);
            const draws = played - wins - losses;
            const goalsFor = wins * 2 + draws + Math.floor(Math.random() * 10);
            const goalsAgainst = losses * 2 + Math.floor(Math.random() * goalsFor * 0.8);

            // Generate realistic form (last 5 matches)
            const formResults = ['W', 'L', 'D'];
            const form = Array.from({ length: 5 }, () =>
                  formResults[Math.floor(Math.random() * formResults.length)]
            ) as ('W' | 'L' | 'D')[];

            return {
                  position: index + 1,
                  team,
                  points: wins * 3 + draws,
                  playedGames: played,
                  wins,
                  draws,
                  losses,
                  goalsFor,
                  goalsAgainst,
                  goalDifference: goalsFor - goalsAgainst,
                  form,
                  externalId: parseInt(team.id),
                  teamId: parseInt(team.id),
                  teamName: team.name,
                  teamLogo: team.logo,
            };
      }).sort((a, b) => b.points - a.points || b.goalDifference - a.goalDifference);
};

export const useLeagueStandings = ({
      leagueId,
      season,
      format = 'external'
}: UseLeagueStandingsProps): UseLeagueStandingsReturn => {
      const [standings, setStandings] = useState<StandingData[]>([]);
      const [loading, setLoading] = useState(true);
      const [error, setError] = useState<string | null>(null);

      console.log('🏗️ useLeagueStandings hook initialized:', { leagueId, season, format });

      const refreshStandings = async () => {
            try {
                  setLoading(true);
                  setError(null);
                  const standingsData = await fetchStandingsFromAPI(leagueId, season, format);
                  console.log('🎯 Setting standings data in hook:', standingsData.length, 'teams');
                  console.log('🎯 First team in standings:', standingsData[0]?.team?.name);
                  setStandings(standingsData);
            } catch (err) {
                  console.error('❌ Error fetching standings:', err);
                  setError(err instanceof Error ? err.message : 'Failed to fetch standings');

                  // In development, don't use fallback to debug the real issue
                  if (process.env.NODE_ENV === 'development') {
                        console.error('🚨 DEVELOPMENT MODE: Not using fallback data to debug API issue');
                        setStandings([]); // Show empty instead of fallback
                  } else {
                        // Use fallback data in case of error
                        console.warn('⚠️ Using fallback standings data due to error');
                        setStandings(generateFallbackStandings(leagueId));
                  }
            } finally {
                  setLoading(false);
            }
      };

      useEffect(() => {
            if (leagueId) {
                  console.log('🔄 useLeagueStandings useEffect triggered:', { leagueId, season, format });
                  // Clear any existing data first
                  setStandings([]);
                  setError(null);
                  refreshStandings();
            }
      }, [leagueId, season, format]);

      // Calculate statistics
      const stats = useMemo(() => {
            const totalTeams = standings.length;
            const topTeam = standings.length > 0 ? standings[0] : null;

            const totalGames = standings.reduce((sum, team) => sum + team.playedGames, 0);
            const totalGoals = standings.reduce((sum, team) => sum + team.goalsFor, 0);
            const avgGoalsPerGame = totalGames > 0 ? Math.round((totalGoals / totalGames) * 100) / 100 : 0;

            return {
                  totalTeams,
                  topTeam,
                  avgGoalsPerGame,
            };
      }, [standings]);

      return {
            standings,
            loading,
            error,
            refreshStandings,
            stats,
      };
};
