"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/leagues/[id]/route";
exports.ids = ["app/api/leagues/[id]/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fleagues%2F%5Bid%5D%2Froute&page=%2Fapi%2Fleagues%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fleagues%2F%5Bid%5D%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fleagues%2F%5Bid%5D%2Froute&page=%2Fapi%2Fleagues%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fleagues%2F%5Bid%5D%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_duyanhstar_FECMS_sport_src_app_api_leagues_id_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/leagues/[id]/route.ts */ \"(rsc)/./src/app/api/leagues/[id]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/leagues/[id]/route\",\n        pathname: \"/api/leagues/[id]\",\n        filename: \"route\",\n        bundlePath: \"app/api/leagues/[id]/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/FECMS-sport/src/app/api/leagues/[id]/route.ts\",\n    nextConfigOutput,\n    userland: _home_duyanhstar_FECMS_sport_src_app_api_leagues_id_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/leagues/[id]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fleagues%2F%5Bid%5D%2Froute&page=%2Fapi%2Fleagues%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fleagues%2F%5Bid%5D%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/leagues/[id]/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/leagues/[id]/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   PATCH: () => (/* binding */ PATCH),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nconst dynamic = \"force-dynamic\";\nconst API_BASE_URL = \"http://localhost:3000\" || 0;\nasync function GET(request, { params }) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const idParam = params.id;\n        // Parse externalId and season from URL parameter (e.g., \"61-2024\")\n        let leagueId;\n        let season;\n        if (idParam.includes(\"-\")) {\n            const [externalId, parsedSeason] = idParam.split(\"-\");\n            leagueId = externalId;\n            season = parsedSeason;\n        } else {\n            leagueId = idParam;\n        }\n        // Build query parameters (include season if parsed from URL)\n        const queryParams = new URLSearchParams();\n        searchParams.forEach((value, key)=>{\n            queryParams.append(key, value);\n        });\n        // Add season from URL if parsed\n        if (season && !queryParams.has(\"season\")) {\n            queryParams.append(\"season\", season);\n        }\n        const queryString = queryParams.toString();\n        const url = `${API_BASE_URL}/football/leagues/${leagueId}${queryString ? `?${queryString}` : \"\"}`;\n        console.log(\"\\uD83D\\uDD04 Proxying league detail request:\", url);\n        const response = await fetch(url, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...request.headers.get(\"authorization\") && {\n                    \"Authorization\": request.headers.get(\"authorization\")\n                }\n            }\n        });\n        if (!response.ok) {\n            console.error(\"❌ API Error:\", response.status, response.statusText);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to fetch league\",\n                status: response.status,\n                message: response.statusText\n            }, {\n                status: response.status\n            });\n        }\n        const data = await response.json();\n        console.log(\"✅ League detail fetched successfully:\", data);\n        // The API returns an array in data field\n        if (data.data && Array.isArray(data.data)) {\n            const leagues = data.data;\n            // If season is specified in URL, find the exact match\n            if (season) {\n                const seasonInt = parseInt(season);\n                const selectedLeague = leagues.find((league)=>league.season === seasonInt);\n                if (selectedLeague) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(selectedLeague);\n                } else {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: `No league data found for season ${season}`\n                    }, {\n                        status: 404\n                    });\n                }\n            }\n            // If no season specified, find the current/active league first\n            let selectedLeague = leagues.find((league)=>league.season_detail?.current === true);\n            // If no current league, get the most recent one (highest season)\n            if (!selectedLeague && leagues.length > 0) {\n                selectedLeague = leagues.reduce((prev, current)=>current.season > prev.season ? current : prev);\n            }\n            if (selectedLeague) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(selectedLeague);\n            } else {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"No league data found\"\n                }, {\n                    status: 404\n                });\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        console.error(\"❌ Proxy Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\",\n            message: error.message\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PATCH(request, { params }) {\n    try {\n        const idParam = params.id;\n        const body = await request.json();\n        // Determine if this is an internal ID or externalId-season format\n        let leagueId;\n        if (idParam.includes(\"-\")) {\n            // Format: \"61-2024\" -> use externalId \"61\"\n            leagueId = idParam.split(\"-\")[0];\n        } else {\n            // Direct internal ID (e.g., \"21\") -> use as is\n            leagueId = idParam;\n        }\n        console.log(\"\\uD83D\\uDD04 Proxying league update request to internal ID:\", leagueId);\n        const response = await fetch(`${API_BASE_URL}/football/leagues/${leagueId}`, {\n            method: \"PATCH\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...request.headers.get(\"authorization\") && {\n                    \"Authorization\": request.headers.get(\"authorization\")\n                }\n            },\n            body: JSON.stringify(body)\n        });\n        if (!response.ok) {\n            console.error(\"❌ API Error:\", response.status, response.statusText);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to update league\",\n                status: response.status,\n                message: response.statusText\n            }, {\n                status: response.status\n            });\n        }\n        const data = await response.json();\n        console.log(\"✅ League updated successfully:\", data.id || data.externalId);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n    } catch (error) {\n        console.error(\"❌ Proxy Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\",\n            message: error.message\n        }, {\n            status: 500\n        });\n    }\n}\nasync function DELETE(request, { params }) {\n    try {\n        const idParam = params.id;\n        // Parse externalId from URL parameter (e.g., \"61-2024\" -> \"61\")\n        const leagueId = idParam.includes(\"-\") ? idParam.split(\"-\")[0] : idParam;\n        console.log(\"\\uD83D\\uDD04 Proxying league delete request:\", leagueId);\n        const response = await fetch(`${API_BASE_URL}/football/leagues/${leagueId}`, {\n            method: \"DELETE\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                ...request.headers.get(\"authorization\") && {\n                    \"Authorization\": request.headers.get(\"authorization\")\n                }\n            }\n        });\n        if (!response.ok) {\n            console.error(\"❌ API Error:\", response.status, response.statusText);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Failed to delete league\",\n                status: response.status,\n                message: response.statusText\n            }, {\n                status: response.status\n            });\n        }\n        console.log(\"✅ League deleted successfully:\", leagueId);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true\n        });\n    } catch (error) {\n        console.error(\"❌ Proxy Error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Internal server error\",\n            message: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/leagues/[id]/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fleagues%2F%5Bid%5D%2Froute&page=%2Fapi%2Fleagues%2F%5Bid%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fleagues%2F%5Bid%5D%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();