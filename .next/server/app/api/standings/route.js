"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/standings/route";
exports.ids = ["app/api/standings/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstandings%2Froute&page=%2Fapi%2Fstandings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstandings%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstandings%2Froute&page=%2Fapi%2Fstandings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstandings%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_duyanhstar_FECMS_sport_src_app_api_standings_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/standings/route.ts */ \"(rsc)/./src/app/api/standings/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/standings/route\",\n        pathname: \"/api/standings\",\n        filename: \"route\",\n        bundlePath: \"app/api/standings/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/FECMS-sport/src/app/api/standings/route.ts\",\n    nextConfigOutput,\n    userland: _home_duyanhstar_FECMS_sport_src_app_api_standings_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/standings/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstandings%2Froute&page=%2Fapi%2Fstandings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstandings%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/standings/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/standings/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   dynamic: () => (/* binding */ dynamic)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nconst dynamic = \"force-dynamic\";\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const league = searchParams.get(\"league\");\n        const season = searchParams.get(\"season\");\n        const format = searchParams.get(\"format\");\n        // Remove timestamp parameter as it's only for cache busting\n        searchParams.delete(\"_t\");\n        // Validate required parameters\n        if (!league) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"League parameter is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Build API URL with parameters\n        const apiBaseUrl = \"http://localhost:3000\" || 0;\n        let apiUrl = `${apiBaseUrl}/football/standings?league=${league}`;\n        if (season) {\n            apiUrl += `&season=${season}`;\n        }\n        if (format) {\n            apiUrl += `&format=${format}`;\n        }\n        console.log(\"\\uD83D\\uDD04 Proxying standings request to:\", apiUrl);\n        // Make request to backend API\n        const response = await fetch(apiUrl, {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) {\n            console.error(\"❌ Backend API error:\", response.status, response.statusText);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Backend API error: ${response.status}`\n            }, {\n                status: response.status\n            });\n        }\n        const data = await response.json();\n        console.log(\"✅ Standings API response received\");\n        // Return with no-cache headers to prevent caching issues\n        const jsonResponse = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data);\n        jsonResponse.headers.set(\"Cache-Control\", \"no-cache, no-store, must-revalidate\");\n        jsonResponse.headers.set(\"Pragma\", \"no-cache\");\n        jsonResponse.headers.set(\"Expires\", \"0\");\n        return jsonResponse;\n    } catch (error) {\n        console.error(\"❌ Standings API proxy error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch standings\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/standings/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fstandings%2Froute&page=%2Fapi%2Fstandings%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fstandings%2Froute.ts&appDir=%2Fhome%2Fduyanhstar%2FFECMS-sport%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fduyanhstar%2FFECMS-sport&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();