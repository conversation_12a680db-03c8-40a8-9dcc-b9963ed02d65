"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/leagues/[id]/page",{

/***/ "(app-pages-browser)/./src/lib/hooks/leagues/useLeagueStandings.ts":
/*!*****************************************************!*\
  !*** ./src/lib/hooks/leagues/useLeagueStandings.ts ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLeagueStandings: function() { return /* binding */ useLeagueStandings; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useLeagueStandings auto */ \n// Fetch standings from API\nconst fetchStandingsFromAPI = async (leagueId, season, format)=>{\n    try {\n        var _apiData_data, _apiData_data1;\n        const currentSeason = season || new Date().getFullYear();\n        const formatParam = format ? \"&format=\".concat(format) : \"\";\n        console.log(\"\\uD83D\\uDD04 Fetching standings for league:\", leagueId, \"season:\", currentSeason);\n        // Use proxy endpoint through Next.js frontend with cache busting\n        const timestamp = Date.now();\n        const response = await fetch(\"/api/standings?league=\".concat(leagueId, \"&season=\").concat(currentSeason).concat(formatParam, \"&_t=\").concat(timestamp));\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch standings: \".concat(response.status));\n        }\n        const apiData = await response.json();\n        console.log(\"✅ Standings API response:\", apiData);\n        console.log(\"\\uD83D\\uDD0D API data structure check:\", {\n            hasData: !!apiData.data,\n            isArray: Array.isArray(apiData.data),\n            dataLength: (_apiData_data = apiData.data) === null || _apiData_data === void 0 ? void 0 : _apiData_data.length,\n            firstItem: (_apiData_data1 = apiData.data) === null || _apiData_data1 === void 0 ? void 0 : _apiData_data1[0]\n        });\n        if (!apiData.data || !Array.isArray(apiData.data)) {\n            console.warn(\"⚠️ No standings data in API response, using fallback\");\n            return generateFallbackStandings(leagueId);\n        }\n        // Transform API data to our interface\n        console.log(\"\\uD83D\\uDD04 Transforming API data to interface, count:\", apiData.data.length);\n        const transformedData = apiData.data.map((standing, index)=>{\n            var _standing_teamId, _standing_team_externalId, _standing_team, _standing_team1, _standing_team2, _standing_goals, _standing_goals1, _standing_team3, _standing_team4, _standing_team5;\n            // Parse form data from API (e.g., \"WWWWW\" -> ['W', 'W', 'W', 'W', 'W'])\n            let form = [];\n            if (standing.form && typeof standing.form === \"string\") {\n                form = standing.form.split(\"\").slice(0, 5);\n            } else {\n                // Fallback to random form if not provided\n                const formResults = [\n                    \"W\",\n                    \"L\",\n                    \"D\"\n                ];\n                form = Array.from({\n                    length: 5\n                }, ()=>formResults[Math.floor(Math.random() * formResults.length)]);\n            }\n            return {\n                position: standing.position || standing.rank || index + 1,\n                team: {\n                    id: ((_standing_teamId = standing.teamId) === null || _standing_teamId === void 0 ? void 0 : _standing_teamId.toString()) || ((_standing_team = standing.team) === null || _standing_team === void 0 ? void 0 : (_standing_team_externalId = _standing_team.externalId) === null || _standing_team_externalId === void 0 ? void 0 : _standing_team_externalId.toString()) || (index + 1).toString(),\n                    name: standing.teamName || ((_standing_team1 = standing.team) === null || _standing_team1 === void 0 ? void 0 : _standing_team1.name) || \"Team \".concat(index + 1),\n                    logo: standing.teamLogo || ((_standing_team2 = standing.team) === null || _standing_team2 === void 0 ? void 0 : _standing_team2.logo) || \"\"\n                },\n                points: standing.points || 0,\n                playedGames: standing.played || standing.playedGames || 0,\n                wins: standing.win || standing.wins || 0,\n                draws: standing.draw || standing.draws || 0,\n                losses: standing.lose || standing.losses || 0,\n                goalsFor: standing.goalsFor || ((_standing_goals = standing.goals) === null || _standing_goals === void 0 ? void 0 : _standing_goals.for) || 0,\n                goalsAgainst: standing.goalsAgainst || ((_standing_goals1 = standing.goals) === null || _standing_goals1 === void 0 ? void 0 : _standing_goals1.against) || 0,\n                goalDifference: standing.goalsDiff !== undefined ? standing.goalsDiff : standing.goalDifference !== undefined ? standing.goalDifference : (standing.goalsFor || 0) - (standing.goalsAgainst || 0),\n                form,\n                // Store original API fields\n                externalId: standing.externalId || standing.id,\n                teamId: standing.teamId || ((_standing_team3 = standing.team) === null || _standing_team3 === void 0 ? void 0 : _standing_team3.externalId),\n                teamName: standing.teamName || ((_standing_team4 = standing.team) === null || _standing_team4 === void 0 ? void 0 : _standing_team4.name),\n                teamLogo: standing.teamLogo || ((_standing_team5 = standing.team) === null || _standing_team5 === void 0 ? void 0 : _standing_team5.logo)\n            };\n        });\n        const sortedData = transformedData.sort((a, b)=>a.position - b.position);\n        console.log(\"✅ Transformed standings data:\", sortedData.slice(0, 3).map((s)=>({\n                position: s.position,\n                teamName: s.team.name,\n                points: s.points\n            })));\n        return sortedData;\n    } catch (error) {\n        console.error(\"❌ Error fetching standings:\", error);\n        throw error;\n    }\n};\n// Generate fallback standings data for development/testing\nconst generateFallbackStandings = (leagueId)=>{\n    const teams = [\n        {\n            id: \"33\",\n            name: \"Manchester United\",\n            logo: \"https://media.api-sports.io/football/teams/33.png\"\n        },\n        {\n            id: \"50\",\n            name: \"Manchester City\",\n            logo: \"https://media.api-sports.io/football/teams/50.png\"\n        },\n        {\n            id: \"42\",\n            name: \"Arsenal\",\n            logo: \"https://media.api-sports.io/football/teams/42.png\"\n        },\n        {\n            id: \"40\",\n            name: \"Liverpool\",\n            logo: \"https://media.api-sports.io/football/teams/40.png\"\n        },\n        {\n            id: \"49\",\n            name: \"Chelsea\",\n            logo: \"https://media.api-sports.io/football/teams/49.png\"\n        },\n        {\n            id: \"47\",\n            name: \"Tottenham\",\n            logo: \"https://media.api-sports.io/football/teams/47.png\"\n        },\n        {\n            id: \"34\",\n            name: \"Newcastle\",\n            logo: \"https://media.api-sports.io/football/teams/34.png\"\n        },\n        {\n            id: \"66\",\n            name: \"Aston Villa\",\n            logo: \"https://media.api-sports.io/football/teams/66.png\"\n        },\n        {\n            id: \"51\",\n            name: \"Brighton\",\n            logo: \"https://media.api-sports.io/football/teams/51.png\"\n        },\n        {\n            id: \"39\",\n            name: \"Wolves\",\n            logo: \"https://media.api-sports.io/football/teams/39.png\"\n        }\n    ];\n    return teams.map((team, index)=>{\n        const played = 20 + Math.floor(Math.random() * 10);\n        const wins = Math.floor(Math.random() * played * 0.7);\n        const losses = Math.floor(Math.random() * (played - wins) * 0.6);\n        const draws = played - wins - losses;\n        const goalsFor = wins * 2 + draws + Math.floor(Math.random() * 10);\n        const goalsAgainst = losses * 2 + Math.floor(Math.random() * goalsFor * 0.8);\n        // Generate realistic form (last 5 matches)\n        const formResults = [\n            \"W\",\n            \"L\",\n            \"D\"\n        ];\n        const form = Array.from({\n            length: 5\n        }, ()=>formResults[Math.floor(Math.random() * formResults.length)]);\n        return {\n            position: index + 1,\n            team,\n            points: wins * 3 + draws,\n            playedGames: played,\n            wins,\n            draws,\n            losses,\n            goalsFor,\n            goalsAgainst,\n            goalDifference: goalsFor - goalsAgainst,\n            form,\n            externalId: parseInt(team.id),\n            teamId: parseInt(team.id),\n            teamName: team.name,\n            teamLogo: team.logo\n        };\n    }).sort((a, b)=>b.points - a.points || b.goalDifference - a.goalDifference);\n};\nconst useLeagueStandings = (param)=>{\n    let { leagueId, season, format = \"external\" } = param;\n    const [standings, setStandings] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    console.log(\"\\uD83C\\uDFD7️ useLeagueStandings hook initialized:\", {\n        leagueId,\n        season,\n        format\n    });\n    const refreshStandings = async ()=>{\n        try {\n            var _standingsData__team, _standingsData_;\n            setLoading(true);\n            setError(null);\n            const standingsData = await fetchStandingsFromAPI(leagueId, season, format);\n            console.log(\"\\uD83C\\uDFAF Setting standings data in hook:\", standingsData.length, \"teams\");\n            console.log(\"\\uD83C\\uDFAF First team in standings:\", (_standingsData_ = standingsData[0]) === null || _standingsData_ === void 0 ? void 0 : (_standingsData__team = _standingsData_.team) === null || _standingsData__team === void 0 ? void 0 : _standingsData__team.name);\n            setStandings(standingsData);\n        } catch (err) {\n            console.error(\"❌ Error fetching standings:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to fetch standings\");\n            // Use fallback data in case of error\n            console.warn(\"⚠️ Using fallback standings data due to error\");\n            setStandings(generateFallbackStandings(leagueId));\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (leagueId) {\n            console.log(\"\\uD83D\\uDD04 useLeagueStandings useEffect triggered:\", {\n                leagueId,\n                season,\n                format\n            });\n            // Clear any existing data first\n            setStandings([]);\n            setError(null);\n            refreshStandings();\n        }\n    }, [\n        leagueId,\n        season,\n        format\n    ]);\n    // Calculate statistics\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const totalTeams = standings.length;\n        const topTeam = standings.length > 0 ? standings[0] : null;\n        const totalGames = standings.reduce((sum, team)=>sum + team.playedGames, 0);\n        const totalGoals = standings.reduce((sum, team)=>sum + team.goalsFor, 0);\n        const avgGoalsPerGame = totalGames > 0 ? Math.round(totalGoals / totalGames * 100) / 100 : 0;\n        return {\n            totalTeams,\n            topTeam,\n            avgGoalsPerGame\n        };\n    }, [\n        standings\n    ]);\n    return {\n        standings,\n        loading,\n        error,\n        refreshStandings,\n        stats\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/hooks/leagues/useLeagueStandings.ts\n"));

/***/ })

});