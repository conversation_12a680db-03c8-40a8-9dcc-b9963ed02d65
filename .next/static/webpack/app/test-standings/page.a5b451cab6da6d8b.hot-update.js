"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-standings/page",{

/***/ "(app-pages-browser)/./src/lib/hooks/leagues/useLeagueStandings.ts":
/*!*****************************************************!*\
  !*** ./src/lib/hooks/leagues/useLeagueStandings.ts ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLeagueStandings: function() { return /* binding */ useLeagueStandings; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useLeagueStandings auto */ \n// Fetch standings from API\nconst fetchStandingsFromAPI = async (leagueId, season, format)=>{\n    try {\n        var _apiData_response, _apiData_response_, _apiData_response1, _apiData_response__league, _apiData_response_1, _apiData_response2, _standingsArray__team, _standingsArray_;\n        const currentSeason = season || new Date().getFullYear();\n        const formatParam = format ? \"&format=\".concat(format) : \"\";\n        console.log(\"\\uD83D\\uDD04 Fetching standings for league:\", leagueId, \"season:\", currentSeason);\n        // Use proxy endpoint through Next.js frontend with cache busting\n        const timestamp = Date.now();\n        const apiUrl = \"/api/standings?league=\".concat(leagueId, \"&season=\").concat(currentSeason).concat(formatParam, \"&_t=\").concat(timestamp);\n        console.log(\"\\uD83C\\uDF10 Making API call to:\", apiUrl);\n        const response = await fetch(apiUrl, {\n            method: \"GET\",\n            headers: {\n                \"Cache-Control\": \"no-cache, no-store, must-revalidate\",\n                \"Pragma\": \"no-cache\",\n                \"Expires\": \"0\"\n            }\n        });\n        if (!response.ok) {\n            console.error(\"❌ API Response not OK:\", response.status, response.statusText);\n            throw new Error(\"Failed to fetch standings: \".concat(response.status, \" \").concat(response.statusText));\n        }\n        const apiData = await response.json();\n        console.log(\"✅ Standings API response:\", apiData);\n        // Extract standings data from the correct path: response[0].league.standings[0]\n        let standingsArray = [];\n        if (apiData.response && Array.isArray(apiData.response) && apiData.response.length > 0) {\n            const leagueData = apiData.response[0];\n            if (leagueData.league && leagueData.league.standings && Array.isArray(leagueData.league.standings)) {\n                standingsArray = leagueData.league.standings[0] || [];\n            }\n        }\n        console.log(\"\\uD83D\\uDD0D Extracted standings data:\", {\n            hasResponse: !!apiData.response,\n            responseLength: (_apiData_response = apiData.response) === null || _apiData_response === void 0 ? void 0 : _apiData_response.length,\n            hasLeague: !!((_apiData_response1 = apiData.response) === null || _apiData_response1 === void 0 ? void 0 : (_apiData_response_ = _apiData_response1[0]) === null || _apiData_response_ === void 0 ? void 0 : _apiData_response_.league),\n            hasStandings: !!((_apiData_response2 = apiData.response) === null || _apiData_response2 === void 0 ? void 0 : (_apiData_response_1 = _apiData_response2[0]) === null || _apiData_response_1 === void 0 ? void 0 : (_apiData_response__league = _apiData_response_1.league) === null || _apiData_response__league === void 0 ? void 0 : _apiData_response__league.standings),\n            standingsLength: standingsArray.length,\n            firstTeam: (_standingsArray_ = standingsArray[0]) === null || _standingsArray_ === void 0 ? void 0 : (_standingsArray__team = _standingsArray_.team) === null || _standingsArray__team === void 0 ? void 0 : _standingsArray__team.name\n        });\n        if (!standingsArray || !Array.isArray(standingsArray) || standingsArray.length === 0) {\n            console.error(\"❌ No standings data found in API response:\", {\n                responseStructure: Object.keys(apiData),\n                standingsArray: standingsArray\n            });\n            // In development, throw error instead of using fallback\n            if (true) {\n                throw new Error(\"No standings data found. Response structure: \".concat(Object.keys(apiData).join(\", \")));\n            }\n            console.warn(\"⚠️ No standings data in API response, using fallback\");\n            return generateFallbackStandings(leagueId);\n        }\n        // Transform API data to our interface\n        console.log(\"\\uD83D\\uDD04 Transforming API data to interface, count:\", apiData.data.length);\n        const transformedData = apiData.data.map((standing, index)=>{\n            var _standing_teamId, _standing_team_externalId, _standing_team, _standing_team1, _standing_team2, _standing_goals, _standing_goals1, _standing_team3, _standing_team4, _standing_team5;\n            // Parse form data from API (e.g., \"WWWWW\" -> ['W', 'W', 'W', 'W', 'W'])\n            let form = [];\n            if (standing.form && typeof standing.form === \"string\") {\n                form = standing.form.split(\"\").slice(0, 5);\n            } else {\n                // Fallback to random form if not provided\n                const formResults = [\n                    \"W\",\n                    \"L\",\n                    \"D\"\n                ];\n                form = Array.from({\n                    length: 5\n                }, ()=>formResults[Math.floor(Math.random() * formResults.length)]);\n            }\n            return {\n                position: standing.position || standing.rank || index + 1,\n                team: {\n                    id: ((_standing_teamId = standing.teamId) === null || _standing_teamId === void 0 ? void 0 : _standing_teamId.toString()) || ((_standing_team = standing.team) === null || _standing_team === void 0 ? void 0 : (_standing_team_externalId = _standing_team.externalId) === null || _standing_team_externalId === void 0 ? void 0 : _standing_team_externalId.toString()) || (index + 1).toString(),\n                    name: standing.teamName || ((_standing_team1 = standing.team) === null || _standing_team1 === void 0 ? void 0 : _standing_team1.name) || \"Team \".concat(index + 1),\n                    logo: standing.teamLogo || ((_standing_team2 = standing.team) === null || _standing_team2 === void 0 ? void 0 : _standing_team2.logo) || \"\"\n                },\n                points: standing.points || 0,\n                playedGames: standing.played || standing.playedGames || 0,\n                wins: standing.win || standing.wins || 0,\n                draws: standing.draw || standing.draws || 0,\n                losses: standing.lose || standing.losses || 0,\n                goalsFor: standing.goalsFor || ((_standing_goals = standing.goals) === null || _standing_goals === void 0 ? void 0 : _standing_goals.for) || 0,\n                goalsAgainst: standing.goalsAgainst || ((_standing_goals1 = standing.goals) === null || _standing_goals1 === void 0 ? void 0 : _standing_goals1.against) || 0,\n                goalDifference: standing.goalsDiff !== undefined ? standing.goalsDiff : standing.goalDifference !== undefined ? standing.goalDifference : (standing.goalsFor || 0) - (standing.goalsAgainst || 0),\n                form,\n                // Store original API fields\n                externalId: standing.externalId || standing.id,\n                teamId: standing.teamId || ((_standing_team3 = standing.team) === null || _standing_team3 === void 0 ? void 0 : _standing_team3.externalId),\n                teamName: standing.teamName || ((_standing_team4 = standing.team) === null || _standing_team4 === void 0 ? void 0 : _standing_team4.name),\n                teamLogo: standing.teamLogo || ((_standing_team5 = standing.team) === null || _standing_team5 === void 0 ? void 0 : _standing_team5.logo)\n            };\n        });\n        const sortedData = transformedData.sort((a, b)=>a.position - b.position);\n        console.log(\"✅ Transformed standings data:\", sortedData.slice(0, 3).map((s)=>({\n                position: s.position,\n                teamName: s.team.name,\n                points: s.points\n            })));\n        return sortedData;\n    } catch (error) {\n        console.error(\"❌ Error fetching standings:\", error);\n        throw error;\n    }\n};\n// Generate fallback standings data for development/testing\nconst generateFallbackStandings = (leagueId)=>{\n    const teams = [\n        {\n            id: \"33\",\n            name: \"Manchester United\",\n            logo: \"https://media.api-sports.io/football/teams/33.png\"\n        },\n        {\n            id: \"50\",\n            name: \"Manchester City\",\n            logo: \"https://media.api-sports.io/football/teams/50.png\"\n        },\n        {\n            id: \"42\",\n            name: \"Arsenal\",\n            logo: \"https://media.api-sports.io/football/teams/42.png\"\n        },\n        {\n            id: \"40\",\n            name: \"Liverpool\",\n            logo: \"https://media.api-sports.io/football/teams/40.png\"\n        },\n        {\n            id: \"49\",\n            name: \"Chelsea\",\n            logo: \"https://media.api-sports.io/football/teams/49.png\"\n        },\n        {\n            id: \"47\",\n            name: \"Tottenham\",\n            logo: \"https://media.api-sports.io/football/teams/47.png\"\n        },\n        {\n            id: \"34\",\n            name: \"Newcastle\",\n            logo: \"https://media.api-sports.io/football/teams/34.png\"\n        },\n        {\n            id: \"66\",\n            name: \"Aston Villa\",\n            logo: \"https://media.api-sports.io/football/teams/66.png\"\n        },\n        {\n            id: \"51\",\n            name: \"Brighton\",\n            logo: \"https://media.api-sports.io/football/teams/51.png\"\n        },\n        {\n            id: \"39\",\n            name: \"Wolves\",\n            logo: \"https://media.api-sports.io/football/teams/39.png\"\n        }\n    ];\n    return teams.map((team, index)=>{\n        const played = 20 + Math.floor(Math.random() * 10);\n        const wins = Math.floor(Math.random() * played * 0.7);\n        const losses = Math.floor(Math.random() * (played - wins) * 0.6);\n        const draws = played - wins - losses;\n        const goalsFor = wins * 2 + draws + Math.floor(Math.random() * 10);\n        const goalsAgainst = losses * 2 + Math.floor(Math.random() * goalsFor * 0.8);\n        // Generate realistic form (last 5 matches)\n        const formResults = [\n            \"W\",\n            \"L\",\n            \"D\"\n        ];\n        const form = Array.from({\n            length: 5\n        }, ()=>formResults[Math.floor(Math.random() * formResults.length)]);\n        return {\n            position: index + 1,\n            team,\n            points: wins * 3 + draws,\n            playedGames: played,\n            wins,\n            draws,\n            losses,\n            goalsFor,\n            goalsAgainst,\n            goalDifference: goalsFor - goalsAgainst,\n            form,\n            externalId: parseInt(team.id),\n            teamId: parseInt(team.id),\n            teamName: team.name,\n            teamLogo: team.logo\n        };\n    }).sort((a, b)=>b.points - a.points || b.goalDifference - a.goalDifference);\n};\nconst useLeagueStandings = (param)=>{\n    let { leagueId, season, format = \"external\" } = param;\n    const [standings, setStandings] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    console.log(\"\\uD83C\\uDFD7️ useLeagueStandings hook initialized:\", {\n        leagueId,\n        season,\n        format\n    });\n    const refreshStandings = async ()=>{\n        try {\n            var _standingsData__team, _standingsData_;\n            setLoading(true);\n            setError(null);\n            const standingsData = await fetchStandingsFromAPI(leagueId, season, format);\n            console.log(\"\\uD83C\\uDFAF Setting standings data in hook:\", standingsData.length, \"teams\");\n            console.log(\"\\uD83C\\uDFAF First team in standings:\", (_standingsData_ = standingsData[0]) === null || _standingsData_ === void 0 ? void 0 : (_standingsData__team = _standingsData_.team) === null || _standingsData__team === void 0 ? void 0 : _standingsData__team.name);\n            setStandings(standingsData);\n        } catch (err) {\n            console.error(\"❌ Error fetching standings:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to fetch standings\");\n            // In development, don't use fallback to debug the real issue\n            if (true) {\n                console.error(\"\\uD83D\\uDEA8 DEVELOPMENT MODE: Not using fallback data to debug API issue\");\n                setStandings([]); // Show empty instead of fallback\n            } else {}\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (leagueId) {\n            console.log(\"\\uD83D\\uDD04 useLeagueStandings useEffect triggered:\", {\n                leagueId,\n                season,\n                format\n            });\n            // Clear any existing data first\n            setStandings([]);\n            setError(null);\n            refreshStandings();\n        }\n    }, [\n        leagueId,\n        season,\n        format\n    ]);\n    // Calculate statistics\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const totalTeams = standings.length;\n        const topTeam = standings.length > 0 ? standings[0] : null;\n        const totalGames = standings.reduce((sum, team)=>sum + team.playedGames, 0);\n        const totalGoals = standings.reduce((sum, team)=>sum + team.goalsFor, 0);\n        const avgGoalsPerGame = totalGames > 0 ? Math.round(totalGoals / totalGames * 100) / 100 : 0;\n        return {\n            totalTeams,\n            topTeam,\n            avgGoalsPerGame\n        };\n    }, [\n        standings\n    ]);\n    return {\n        standings,\n        loading,\n        error,\n        refreshStandings,\n        stats\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/hooks/leagues/useLeagueStandings.ts\n"));

/***/ })

});