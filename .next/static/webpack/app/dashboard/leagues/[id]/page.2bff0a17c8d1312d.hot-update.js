"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/leagues/[id]/page",{

/***/ "(app-pages-browser)/./src/lib/hooks/leagues/useLeagueTeams.ts":
/*!*************************************************!*\
  !*** ./src/lib/hooks/leagues/useLeagueTeams.ts ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLeagueTeams: function() { return /* binding */ useLeagueTeams; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useLeagueTeams,default auto */ \n// Fetch teams from API\nconst fetchTeamsFromAPI = async (leagueId, season)=>{\n    try {\n        const currentSeason = season || new Date().getFullYear();\n        console.log(\"\\uD83D\\uDD04 Fetching teams for league:\", leagueId, \"season:\", currentSeason);\n        // Use proxy endpoint through Next.js frontend with higher limit to get all teams\n        const response = await fetch(\"/api/teams?league=\".concat(leagueId, \"&season=\").concat(currentSeason, \"&limit=50\"));\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch teams: \".concat(response.status));\n        }\n        const apiData = await response.json();\n        console.log(\"✅ Teams API response:\", apiData);\n        if (!apiData.data || !Array.isArray(apiData.data)) {\n            console.warn(\"⚠️ No teams data in API response, using fallback\");\n            return generateFallbackTeams(leagueId);\n        }\n        // Transform API data to our interface\n        return apiData.data.map((team)=>{\n            var _team_externalId, _team_id, _team_venue, _team_venue1, _team_venue2;\n            return {\n                id: ((_team_externalId = team.externalId) === null || _team_externalId === void 0 ? void 0 : _team_externalId.toString()) || ((_team_id = team.id) === null || _team_id === void 0 ? void 0 : _team_id.toString()) || Math.random().toString(),\n                name: team.name || \"Unknown Team\",\n                logo: team.logo || \"\",\n                foundedYear: team.founded || new Date().getFullYear(),\n                country: team.country || \"Unknown\",\n                city: ((_team_venue = team.venue) === null || _team_venue === void 0 ? void 0 : _team_venue.city) || \"Unknown\",\n                stadium: ((_team_venue1 = team.venue) === null || _team_venue1 === void 0 ? void 0 : _team_venue1.name) || \"Unknown Stadium\",\n                capacity: ((_team_venue2 = team.venue) === null || _team_venue2 === void 0 ? void 0 : _team_venue2.capacity) || 0,\n                website: \"\",\n                description: \"\",\n                stats: {\n                    matchesPlayed: 0,\n                    wins: 0,\n                    draws: 0,\n                    losses: 0,\n                    goalsFor: 0,\n                    goalsAgainst: 0,\n                    points: 0,\n                    position: undefined\n                },\n                recentForm: [],\n                manager: \"\",\n                playersCount: 0,\n                externalId: team.externalId,\n                code: team.code,\n                venue: team.venue ? {\n                    id: team.venue.id,\n                    name: team.venue.name,\n                    address: team.venue.address,\n                    city: team.venue.city,\n                    capacity: team.venue.capacity,\n                    surface: team.venue.surface,\n                    image: team.venue.image\n                } : undefined,\n                founded: team.founded\n            };\n        });\n    } catch (error) {\n        console.error(\"❌ Error fetching teams:\", error);\n        // Return fallback data on error\n        return generateFallbackTeams(leagueId);\n    }\n};\n// Fallback teams data when API fails\nconst generateFallbackTeams = (leagueId)=>{\n    const teamNames = [\n        \"Arsenal FC\",\n        \"Manchester United\",\n        \"Liverpool FC\",\n        \"Chelsea FC\",\n        \"Manchester City\",\n        \"Tottenham Hotspur\",\n        \"Newcastle United\",\n        \"Brighton & Hove\",\n        \"Aston Villa\",\n        \"West Ham United\",\n        \"Crystal Palace\",\n        \"Fulham FC\",\n        \"Brentford FC\",\n        \"Wolverhampton\",\n        \"Everton FC\",\n        \"Nottingham Forest\",\n        \"Bournemouth AFC\",\n        \"Sheffield United\",\n        \"Burnley FC\",\n        \"Luton Town\"\n    ];\n    const countries = [\n        \"England\",\n        \"Scotland\",\n        \"Wales\",\n        \"Ireland\"\n    ];\n    const cities = [\n        \"London\",\n        \"Manchester\",\n        \"Liverpool\",\n        \"Birmingham\",\n        \"Newcastle\",\n        \"Brighton\",\n        \"Sheffield\",\n        \"Burnley\",\n        \"Luton\",\n        \"Bournemouth\"\n    ];\n    const stadiums = [\n        \"Emirates Stadium\",\n        \"Old Trafford\",\n        \"Anfield\",\n        \"Stamford Bridge\",\n        \"Etihad Stadium\",\n        \"Tottenham Hotspur Stadium\",\n        \"St. James' Park\",\n        \"American Express Community Stadium\",\n        \"Villa Park\",\n        \"London Stadium\"\n    ];\n    const managers = [\n        \"Mikel Arteta\",\n        \"Erik ten Hag\",\n        \"J\\xfcrgen Klopp\",\n        \"Mauricio Pochettino\",\n        \"Pep Guardiola\",\n        \"Ange Postecoglou\",\n        \"Eddie Howe\",\n        \"Roberto De Zerbi\",\n        \"Unai Emery\",\n        \"David Moyes\",\n        \"Roy Hodgson\",\n        \"Marco Silva\"\n    ];\n    return teamNames.map((name, index)=>{\n        const matchesPlayed = Math.floor(Math.random() * 38) + 10;\n        const wins = Math.floor(Math.random() * matchesPlayed * 0.6);\n        const losses = Math.floor(Math.random() * (matchesPlayed - wins) * 0.7);\n        const draws = matchesPlayed - wins - losses;\n        const points = wins * 3 + draws;\n        const goalsFor = Math.floor(Math.random() * 80) + 20;\n        const goalsAgainst = Math.floor(Math.random() * 60) + 15;\n        // Generate recent form (last 5 matches)\n        const recentForm = [];\n        for(let i = 0; i < 5; i++){\n            const rand = Math.random();\n            if (rand < 0.4) recentForm.push(\"W\");\n            else if (rand < 0.7) recentForm.push(\"D\");\n            else recentForm.push(\"L\");\n        }\n        return {\n            id: \"team-\".concat(leagueId, \"-\").concat(index + 1),\n            name,\n            logo: \"/images/teams/\".concat(name.toLowerCase().replace(/\\s+/g, \"-\"), \".png\"),\n            foundedYear: Math.floor(Math.random() * 120) + 1880,\n            country: countries[Math.floor(Math.random() * countries.length)],\n            city: cities[Math.floor(Math.random() * cities.length)],\n            stadium: stadiums[Math.floor(Math.random() * stadiums.length)],\n            capacity: Math.floor(Math.random() * 60000) + 20000,\n            website: \"https://\".concat(name.toLowerCase().replace(/\\s+/g, \"\"), \".com\"),\n            description: \"\".concat(name, \" is one of the most prestigious football clubs with a rich history and passionate fanbase.\"),\n            stats: {\n                matchesPlayed,\n                wins,\n                draws,\n                losses,\n                goalsFor,\n                goalsAgainst,\n                points,\n                position: index + 1\n            },\n            recentForm,\n            manager: managers[Math.floor(Math.random() * managers.length)],\n            playersCount: Math.floor(Math.random() * 10) + 25\n        };\n    }).sort((a, b)=>b.stats.points - a.stats.points); // Sort by points initially\n};\nconst useLeagueTeams = (param)=>{\n    let { leagueId, season } = param;\n    const [teams, setTeams] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"position\");\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"asc\");\n    const [countryFilter, setCountryFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n    // Simulate API call\n    const fetchTeams = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Fetch teams from real API\n            const apiTeams = await fetchTeamsFromAPI(leagueId, season);\n            setTeams(apiTeams);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to fetch teams\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (leagueId) {\n            fetchTeams();\n        }\n    }, [\n        leagueId,\n        season\n    ]);\n    // Filter and sort teams\n    const filteredAndSortedTeams = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        let filtered = teams;\n        // Apply search filter\n        if (searchTerm) {\n            filtered = filtered.filter((team)=>team.name.toLowerCase().includes(searchTerm.toLowerCase()) || team.country.toLowerCase().includes(searchTerm.toLowerCase()) || team.city.toLowerCase().includes(searchTerm.toLowerCase()));\n        }\n        // Apply country filter\n        if (countryFilter) {\n            filtered = filtered.filter((team)=>team.country === countryFilter);\n        }\n        // Apply sorting\n        const sorted = [\n            ...filtered\n        ].sort((a, b)=>{\n            let aValue, bValue;\n            switch(sortBy){\n                case \"name\":\n                    aValue = a.name.toLowerCase();\n                    bValue = b.name.toLowerCase();\n                    break;\n                case \"foundedYear\":\n                    aValue = a.foundedYear;\n                    bValue = b.foundedYear;\n                    break;\n                case \"country\":\n                    aValue = a.country.toLowerCase();\n                    bValue = b.country.toLowerCase();\n                    break;\n                case \"points\":\n                    aValue = a.stats.points;\n                    bValue = b.stats.points;\n                    break;\n                case \"position\":\n                    aValue = a.stats.position || 999;\n                    bValue = b.stats.position || 999;\n                    break;\n                default:\n                    return 0;\n            }\n            if (aValue < bValue) return sortOrder === \"asc\" ? -1 : 1;\n            if (aValue > bValue) return sortOrder === \"asc\" ? 1 : -1;\n            return 0;\n        });\n        return sorted;\n    }, [\n        teams,\n        searchTerm,\n        countryFilter,\n        sortBy,\n        sortOrder\n    ]);\n    // Get unique countries\n    const countries = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const uniqueCountries = Array.from(new Set(teams.map((team)=>team.country)));\n        return uniqueCountries.sort();\n    }, [\n        teams\n    ]);\n    // Calculate statistics\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const totalTeams = teams.length;\n        const totalCountries = countries.length;\n        const averageFoundedYear = totalTeams > 0 ? Math.round(teams.reduce((sum, team)=>sum + team.foundedYear, 0) / totalTeams) : 0;\n        const totalMatches = teams.reduce((sum, team)=>sum + team.stats.matchesPlayed, 0);\n        return {\n            totalTeams,\n            totalCountries,\n            averageFoundedYear,\n            totalMatches\n        };\n    }, [\n        teams,\n        countries\n    ]);\n    const refreshTeams = async ()=>{\n        await fetchTeams();\n    };\n    return {\n        teams,\n        loading,\n        error,\n        searchTerm,\n        setSearchTerm,\n        sortBy,\n        setSortBy,\n        sortOrder,\n        setSortOrder,\n        countryFilter,\n        setCountryFilter,\n        filteredAndSortedTeams,\n        countries,\n        stats,\n        refreshTeams\n    };\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (useLeagueTeams);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/hooks/leagues/useLeagueTeams.ts\n"));

/***/ })

});