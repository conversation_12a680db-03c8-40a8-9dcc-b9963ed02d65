"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/leagues/[id]/page",{

/***/ "(app-pages-browser)/./src/lib/hooks/leagues/useLeagueTeams.ts":
/*!*************************************************!*\
  !*** ./src/lib/hooks/leagues/useLeagueTeams.ts ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLeagueTeams: function() { return /* binding */ useLeagueTeams; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useLeagueTeams,default auto */ \n// Fetch teams from API\nconst fetchTeamsFromAPI = async (leagueId, season)=>{\n    try {\n        const currentSeason = season || new Date().getFullYear();\n        console.log(\"\\uD83D\\uDD04 Fetching teams for league:\", leagueId, \"season:\", currentSeason);\n        // Use proxy endpoint through Next.js frontend with higher limit to get all teams\n        const timestamp = Date.now();\n        const response = await fetch(\"/api/teams?league=\".concat(leagueId, \"&season=\").concat(currentSeason, \"&limit=50&_t=\").concat(timestamp));\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch teams: \".concat(response.status));\n        }\n        const apiData = await response.json();\n        console.log(\"✅ Teams API response:\", apiData);\n        if (!apiData.data || !Array.isArray(apiData.data)) {\n            console.warn(\"⚠️ No teams data in API response, using fallback\");\n            return generateFallbackTeams(leagueId);\n        }\n        // Transform API data to our interface\n        return apiData.data.map((team)=>{\n            var _team_externalId, _team_id, _team_venue, _team_venue1, _team_venue2;\n            return {\n                id: ((_team_externalId = team.externalId) === null || _team_externalId === void 0 ? void 0 : _team_externalId.toString()) || ((_team_id = team.id) === null || _team_id === void 0 ? void 0 : _team_id.toString()) || Math.random().toString(),\n                name: team.name || \"Unknown Team\",\n                logo: team.logo || \"\",\n                foundedYear: team.founded || new Date().getFullYear(),\n                country: team.country || \"Unknown\",\n                city: ((_team_venue = team.venue) === null || _team_venue === void 0 ? void 0 : _team_venue.city) || \"Unknown\",\n                stadium: ((_team_venue1 = team.venue) === null || _team_venue1 === void 0 ? void 0 : _team_venue1.name) || \"Unknown Stadium\",\n                capacity: ((_team_venue2 = team.venue) === null || _team_venue2 === void 0 ? void 0 : _team_venue2.capacity) || 0,\n                website: \"\",\n                description: \"\",\n                stats: {\n                    matchesPlayed: 0,\n                    wins: 0,\n                    draws: 0,\n                    losses: 0,\n                    goalsFor: 0,\n                    goalsAgainst: 0,\n                    points: 0,\n                    position: undefined\n                },\n                recentForm: [],\n                manager: \"\",\n                playersCount: 0,\n                externalId: team.externalId,\n                code: team.code,\n                venue: team.venue ? {\n                    id: team.venue.id,\n                    name: team.venue.name,\n                    address: team.venue.address,\n                    city: team.venue.city,\n                    capacity: team.venue.capacity,\n                    surface: team.venue.surface,\n                    image: team.venue.image\n                } : undefined,\n                founded: team.founded\n            };\n        });\n    } catch (error) {\n        console.error(\"❌ Error fetching teams:\", error);\n        // Return fallback data on error\n        return generateFallbackTeams(leagueId);\n    }\n};\n// Fallback teams data when API fails\nconst generateFallbackTeams = (leagueId)=>{\n    const teamNames = [\n        \"Arsenal FC\",\n        \"Manchester United\",\n        \"Liverpool FC\",\n        \"Chelsea FC\",\n        \"Manchester City\",\n        \"Tottenham Hotspur\",\n        \"Newcastle United\",\n        \"Brighton & Hove\",\n        \"Aston Villa\",\n        \"West Ham United\",\n        \"Crystal Palace\",\n        \"Fulham FC\",\n        \"Brentford FC\",\n        \"Wolverhampton\",\n        \"Everton FC\",\n        \"Nottingham Forest\",\n        \"Bournemouth AFC\",\n        \"Sheffield United\",\n        \"Burnley FC\",\n        \"Luton Town\"\n    ];\n    const countries = [\n        \"England\",\n        \"Scotland\",\n        \"Wales\",\n        \"Ireland\"\n    ];\n    const cities = [\n        \"London\",\n        \"Manchester\",\n        \"Liverpool\",\n        \"Birmingham\",\n        \"Newcastle\",\n        \"Brighton\",\n        \"Sheffield\",\n        \"Burnley\",\n        \"Luton\",\n        \"Bournemouth\"\n    ];\n    const stadiums = [\n        \"Emirates Stadium\",\n        \"Old Trafford\",\n        \"Anfield\",\n        \"Stamford Bridge\",\n        \"Etihad Stadium\",\n        \"Tottenham Hotspur Stadium\",\n        \"St. James' Park\",\n        \"American Express Community Stadium\",\n        \"Villa Park\",\n        \"London Stadium\"\n    ];\n    const managers = [\n        \"Mikel Arteta\",\n        \"Erik ten Hag\",\n        \"J\\xfcrgen Klopp\",\n        \"Mauricio Pochettino\",\n        \"Pep Guardiola\",\n        \"Ange Postecoglou\",\n        \"Eddie Howe\",\n        \"Roberto De Zerbi\",\n        \"Unai Emery\",\n        \"David Moyes\",\n        \"Roy Hodgson\",\n        \"Marco Silva\"\n    ];\n    return teamNames.map((name, index)=>{\n        const matchesPlayed = Math.floor(Math.random() * 38) + 10;\n        const wins = Math.floor(Math.random() * matchesPlayed * 0.6);\n        const losses = Math.floor(Math.random() * (matchesPlayed - wins) * 0.7);\n        const draws = matchesPlayed - wins - losses;\n        const points = wins * 3 + draws;\n        const goalsFor = Math.floor(Math.random() * 80) + 20;\n        const goalsAgainst = Math.floor(Math.random() * 60) + 15;\n        // Generate recent form (last 5 matches)\n        const recentForm = [];\n        for(let i = 0; i < 5; i++){\n            const rand = Math.random();\n            if (rand < 0.4) recentForm.push(\"W\");\n            else if (rand < 0.7) recentForm.push(\"D\");\n            else recentForm.push(\"L\");\n        }\n        return {\n            id: \"team-\".concat(leagueId, \"-\").concat(index + 1),\n            name,\n            logo: \"/images/teams/\".concat(name.toLowerCase().replace(/\\s+/g, \"-\"), \".png\"),\n            foundedYear: Math.floor(Math.random() * 120) + 1880,\n            country: countries[Math.floor(Math.random() * countries.length)],\n            city: cities[Math.floor(Math.random() * cities.length)],\n            stadium: stadiums[Math.floor(Math.random() * stadiums.length)],\n            capacity: Math.floor(Math.random() * 60000) + 20000,\n            website: \"https://\".concat(name.toLowerCase().replace(/\\s+/g, \"\"), \".com\"),\n            description: \"\".concat(name, \" is one of the most prestigious football clubs with a rich history and passionate fanbase.\"),\n            stats: {\n                matchesPlayed,\n                wins,\n                draws,\n                losses,\n                goalsFor,\n                goalsAgainst,\n                points,\n                position: index + 1\n            },\n            recentForm,\n            manager: managers[Math.floor(Math.random() * managers.length)],\n            playersCount: Math.floor(Math.random() * 10) + 25\n        };\n    }).sort((a, b)=>b.stats.points - a.stats.points); // Sort by points initially\n};\nconst useLeagueTeams = (param)=>{\n    let { leagueId, season } = param;\n    const [teams, setTeams] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"position\");\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"asc\");\n    const [countryFilter, setCountryFilter] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n    // Simulate API call\n    const fetchTeams = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            // Fetch teams from real API\n            const apiTeams = await fetchTeamsFromAPI(leagueId, season);\n            setTeams(apiTeams);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : \"Failed to fetch teams\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (leagueId) {\n            fetchTeams();\n        }\n    }, [\n        leagueId,\n        season\n    ]);\n    // Filter and sort teams\n    const filteredAndSortedTeams = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        let filtered = teams;\n        // Apply search filter\n        if (searchTerm) {\n            filtered = filtered.filter((team)=>team.name.toLowerCase().includes(searchTerm.toLowerCase()) || team.country.toLowerCase().includes(searchTerm.toLowerCase()) || team.city.toLowerCase().includes(searchTerm.toLowerCase()));\n        }\n        // Apply country filter\n        if (countryFilter) {\n            filtered = filtered.filter((team)=>team.country === countryFilter);\n        }\n        // Apply sorting\n        const sorted = [\n            ...filtered\n        ].sort((a, b)=>{\n            let aValue, bValue;\n            switch(sortBy){\n                case \"name\":\n                    aValue = a.name.toLowerCase();\n                    bValue = b.name.toLowerCase();\n                    break;\n                case \"foundedYear\":\n                    aValue = a.foundedYear;\n                    bValue = b.foundedYear;\n                    break;\n                case \"country\":\n                    aValue = a.country.toLowerCase();\n                    bValue = b.country.toLowerCase();\n                    break;\n                case \"points\":\n                    aValue = a.stats.points;\n                    bValue = b.stats.points;\n                    break;\n                case \"position\":\n                    aValue = a.stats.position || 999;\n                    bValue = b.stats.position || 999;\n                    break;\n                default:\n                    return 0;\n            }\n            if (aValue < bValue) return sortOrder === \"asc\" ? -1 : 1;\n            if (aValue > bValue) return sortOrder === \"asc\" ? 1 : -1;\n            return 0;\n        });\n        return sorted;\n    }, [\n        teams,\n        searchTerm,\n        countryFilter,\n        sortBy,\n        sortOrder\n    ]);\n    // Get unique countries\n    const countries = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const uniqueCountries = Array.from(new Set(teams.map((team)=>team.country)));\n        return uniqueCountries.sort();\n    }, [\n        teams\n    ]);\n    // Calculate statistics\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const totalTeams = teams.length;\n        const totalCountries = countries.length;\n        const averageFoundedYear = totalTeams > 0 ? Math.round(teams.reduce((sum, team)=>sum + team.foundedYear, 0) / totalTeams) : 0;\n        const totalMatches = teams.reduce((sum, team)=>sum + team.stats.matchesPlayed, 0);\n        return {\n            totalTeams,\n            totalCountries,\n            averageFoundedYear,\n            totalMatches\n        };\n    }, [\n        teams,\n        countries\n    ]);\n    const refreshTeams = async ()=>{\n        await fetchTeams();\n    };\n    return {\n        teams,\n        loading,\n        error,\n        searchTerm,\n        setSearchTerm,\n        sortBy,\n        setSortBy,\n        sortOrder,\n        setSortOrder,\n        countryFilter,\n        setCountryFilter,\n        filteredAndSortedTeams,\n        countries,\n        stats,\n        refreshTeams\n    };\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (useLeagueTeams);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/hooks/leagues/useLeagueTeams.ts\n"));

/***/ })

});