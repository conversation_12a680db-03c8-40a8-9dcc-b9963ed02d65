"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/test-standings/page",{

/***/ "(app-pages-browser)/./src/lib/hooks/leagues/useLeagueStandings.ts":
/*!*****************************************************!*\
  !*** ./src/lib/hooks/leagues/useLeagueStandings.ts ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLeagueStandings: function() { return /* binding */ useLeagueStandings; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useLeagueStandings auto */ \n// Fetch standings from API\nconst fetchStandingsFromAPI = async (leagueId, season, format)=>{\n    try {\n        var _apiData_response, _apiData_response_, _apiData_response1, _apiData_response__league, _apiData_response_1, _apiData_response2, _standingsArray__team, _standingsArray_;\n        const currentSeason = season || new Date().getFullYear();\n        const formatParam = format ? \"&format=\".concat(format) : \"\";\n        console.log(\"\\uD83D\\uDD04 Fetching standings for league:\", leagueId, \"season:\", currentSeason);\n        // Use proxy endpoint through Next.js frontend with cache busting\n        const timestamp = Date.now();\n        const apiUrl = \"/api/standings?league=\".concat(leagueId, \"&season=\").concat(currentSeason).concat(formatParam, \"&_t=\").concat(timestamp);\n        console.log(\"\\uD83C\\uDF10 Making API call to:\", apiUrl);\n        const response = await fetch(apiUrl, {\n            method: \"GET\",\n            headers: {\n                \"Cache-Control\": \"no-cache, no-store, must-revalidate\",\n                \"Pragma\": \"no-cache\",\n                \"Expires\": \"0\"\n            }\n        });\n        if (!response.ok) {\n            console.error(\"❌ API Response not OK:\", response.status, response.statusText);\n            throw new Error(\"Failed to fetch standings: \".concat(response.status, \" \").concat(response.statusText));\n        }\n        const apiData = await response.json();\n        console.log(\"✅ Standings API response:\", apiData);\n        // Extract standings data from the correct path: response[0].league.standings[0]\n        let standingsArray = [];\n        if (apiData.response && Array.isArray(apiData.response) && apiData.response.length > 0) {\n            const leagueData = apiData.response[0];\n            if (leagueData.league && leagueData.league.standings && Array.isArray(leagueData.league.standings)) {\n                standingsArray = leagueData.league.standings[0] || [];\n            }\n        }\n        console.log(\"\\uD83D\\uDD0D Extracted standings data:\", {\n            hasResponse: !!apiData.response,\n            responseLength: (_apiData_response = apiData.response) === null || _apiData_response === void 0 ? void 0 : _apiData_response.length,\n            hasLeague: !!((_apiData_response1 = apiData.response) === null || _apiData_response1 === void 0 ? void 0 : (_apiData_response_ = _apiData_response1[0]) === null || _apiData_response_ === void 0 ? void 0 : _apiData_response_.league),\n            hasStandings: !!((_apiData_response2 = apiData.response) === null || _apiData_response2 === void 0 ? void 0 : (_apiData_response_1 = _apiData_response2[0]) === null || _apiData_response_1 === void 0 ? void 0 : (_apiData_response__league = _apiData_response_1.league) === null || _apiData_response__league === void 0 ? void 0 : _apiData_response__league.standings),\n            standingsLength: standingsArray.length,\n            firstTeam: (_standingsArray_ = standingsArray[0]) === null || _standingsArray_ === void 0 ? void 0 : (_standingsArray__team = _standingsArray_.team) === null || _standingsArray__team === void 0 ? void 0 : _standingsArray__team.name\n        });\n        if (!standingsArray || !Array.isArray(standingsArray) || standingsArray.length === 0) {\n            console.error(\"❌ No standings data found in API response:\", {\n                responseStructure: Object.keys(apiData),\n                standingsArray: standingsArray\n            });\n            // In development, throw error instead of using fallback\n            if (true) {\n                throw new Error(\"No standings data found. Response structure: \".concat(Object.keys(apiData).join(\", \")));\n            }\n            console.warn(\"⚠️ No standings data in API response, using fallback\");\n            return generateFallbackStandings(leagueId);\n        }\n        // Transform API data to our interface\n        console.log(\"\\uD83D\\uDD04 Transforming API data to interface, count:\", standingsArray.length);\n        const transformedData = standingsArray.map((standing, index)=>{\n            var _standing_teamId, _standing_team_externalId, _standing_team, _standing_team1, _standing_team2, _standing_goals, _standing_goals1, _standing_team3, _standing_team4, _standing_team5;\n            // Parse form data from API (e.g., \"WWWWW\" -> ['W', 'W', 'W', 'W', 'W'])\n            let form = [];\n            if (standing.form && typeof standing.form === \"string\") {\n                form = standing.form.split(\"\").slice(0, 5);\n            } else {\n                // Fallback to random form if not provided\n                const formResults = [\n                    \"W\",\n                    \"L\",\n                    \"D\"\n                ];\n                form = Array.from({\n                    length: 5\n                }, ()=>formResults[Math.floor(Math.random() * formResults.length)]);\n            }\n            return {\n                position: standing.position || standing.rank || index + 1,\n                team: {\n                    id: ((_standing_teamId = standing.teamId) === null || _standing_teamId === void 0 ? void 0 : _standing_teamId.toString()) || ((_standing_team = standing.team) === null || _standing_team === void 0 ? void 0 : (_standing_team_externalId = _standing_team.externalId) === null || _standing_team_externalId === void 0 ? void 0 : _standing_team_externalId.toString()) || (index + 1).toString(),\n                    name: standing.teamName || ((_standing_team1 = standing.team) === null || _standing_team1 === void 0 ? void 0 : _standing_team1.name) || \"Team \".concat(index + 1),\n                    logo: standing.teamLogo || ((_standing_team2 = standing.team) === null || _standing_team2 === void 0 ? void 0 : _standing_team2.logo) || \"\"\n                },\n                points: standing.points || 0,\n                playedGames: standing.played || standing.playedGames || 0,\n                wins: standing.win || standing.wins || 0,\n                draws: standing.draw || standing.draws || 0,\n                losses: standing.lose || standing.losses || 0,\n                goalsFor: standing.goalsFor || ((_standing_goals = standing.goals) === null || _standing_goals === void 0 ? void 0 : _standing_goals.for) || 0,\n                goalsAgainst: standing.goalsAgainst || ((_standing_goals1 = standing.goals) === null || _standing_goals1 === void 0 ? void 0 : _standing_goals1.against) || 0,\n                goalDifference: standing.goalsDiff !== undefined ? standing.goalsDiff : standing.goalDifference !== undefined ? standing.goalDifference : (standing.goalsFor || 0) - (standing.goalsAgainst || 0),\n                form,\n                // Store original API fields\n                externalId: standing.externalId || standing.id,\n                teamId: standing.teamId || ((_standing_team3 = standing.team) === null || _standing_team3 === void 0 ? void 0 : _standing_team3.externalId),\n                teamName: standing.teamName || ((_standing_team4 = standing.team) === null || _standing_team4 === void 0 ? void 0 : _standing_team4.name),\n                teamLogo: standing.teamLogo || ((_standing_team5 = standing.team) === null || _standing_team5 === void 0 ? void 0 : _standing_team5.logo)\n            };\n        });\n        const sortedData = transformedData.sort((a, b)=>a.position - b.position);\n        console.log(\"✅ Transformed standings data:\", sortedData.slice(0, 3).map((s)=>({\n                position: s.position,\n                teamName: s.team.name,\n                points: s.points\n            })));\n        return sortedData;\n    } catch (error) {\n        console.error(\"❌ Error fetching standings:\", error);\n        throw error;\n    }\n};\n// Generate fallback standings data for development/testing\nconst generateFallbackStandings = (leagueId)=>{\n    const teams = [\n        {\n            id: \"33\",\n            name: \"Manchester United\",\n            logo: \"https://media.api-sports.io/football/teams/33.png\"\n        },\n        {\n            id: \"50\",\n            name: \"Manchester City\",\n            logo: \"https://media.api-sports.io/football/teams/50.png\"\n        },\n        {\n            id: \"42\",\n            name: \"Arsenal\",\n            logo: \"https://media.api-sports.io/football/teams/42.png\"\n        },\n        {\n            id: \"40\",\n            name: \"Liverpool\",\n            logo: \"https://media.api-sports.io/football/teams/40.png\"\n        },\n        {\n            id: \"49\",\n            name: \"Chelsea\",\n            logo: \"https://media.api-sports.io/football/teams/49.png\"\n        },\n        {\n            id: \"47\",\n            name: \"Tottenham\",\n            logo: \"https://media.api-sports.io/football/teams/47.png\"\n        },\n        {\n            id: \"34\",\n            name: \"Newcastle\",\n            logo: \"https://media.api-sports.io/football/teams/34.png\"\n        },\n        {\n            id: \"66\",\n            name: \"Aston Villa\",\n            logo: \"https://media.api-sports.io/football/teams/66.png\"\n        },\n        {\n            id: \"51\",\n            name: \"Brighton\",\n            logo: \"https://media.api-sports.io/football/teams/51.png\"\n        },\n        {\n            id: \"39\",\n            name: \"Wolves\",\n            logo: \"https://media.api-sports.io/football/teams/39.png\"\n        }\n    ];\n    return teams.map((team, index)=>{\n        const played = 20 + Math.floor(Math.random() * 10);\n        const wins = Math.floor(Math.random() * played * 0.7);\n        const losses = Math.floor(Math.random() * (played - wins) * 0.6);\n        const draws = played - wins - losses;\n        const goalsFor = wins * 2 + draws + Math.floor(Math.random() * 10);\n        const goalsAgainst = losses * 2 + Math.floor(Math.random() * goalsFor * 0.8);\n        // Generate realistic form (last 5 matches)\n        const formResults = [\n            \"W\",\n            \"L\",\n            \"D\"\n        ];\n        const form = Array.from({\n            length: 5\n        }, ()=>formResults[Math.floor(Math.random() * formResults.length)]);\n        return {\n            position: index + 1,\n            team,\n            points: wins * 3 + draws,\n            playedGames: played,\n            wins,\n            draws,\n            losses,\n            goalsFor,\n            goalsAgainst,\n            goalDifference: goalsFor - goalsAgainst,\n            form,\n            externalId: parseInt(team.id),\n            teamId: parseInt(team.id),\n            teamName: team.name,\n            teamLogo: team.logo\n        };\n    }).sort((a, b)=>b.points - a.points || b.goalDifference - a.goalDifference);\n};\nconst useLeagueStandings = (param)=>{\n    let { leagueId, season, format = \"external\" } = param;\n    const [standings, setStandings] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    console.log(\"\\uD83C\\uDFD7️ useLeagueStandings hook initialized:\", {\n        leagueId,\n        season,\n        format\n    });\n    const refreshStandings = async ()=>{\n        try {\n            var _standingsData__team, _standingsData_;\n            setLoading(true);\n            setError(null);\n            const standingsData = await fetchStandingsFromAPI(leagueId, season, format);\n            console.log(\"\\uD83C\\uDFAF Setting standings data in hook:\", standingsData.length, \"teams\");\n            console.log(\"\\uD83C\\uDFAF First team in standings:\", (_standingsData_ = standingsData[0]) === null || _standingsData_ === void 0 ? void 0 : (_standingsData__team = _standingsData_.team) === null || _standingsData__team === void 0 ? void 0 : _standingsData__team.name);\n            setStandings(standingsData);\n        } catch (err) {\n            console.error(\"❌ Error fetching standings:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to fetch standings\");\n            // In development, don't use fallback to debug the real issue\n            if (true) {\n                console.error(\"\\uD83D\\uDEA8 DEVELOPMENT MODE: Not using fallback data to debug API issue\");\n                setStandings([]); // Show empty instead of fallback\n            } else {}\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (leagueId) {\n            console.log(\"\\uD83D\\uDD04 useLeagueStandings useEffect triggered:\", {\n                leagueId,\n                season,\n                format\n            });\n            // Clear any existing data first\n            setStandings([]);\n            setError(null);\n            refreshStandings();\n        }\n    }, [\n        leagueId,\n        season,\n        format\n    ]);\n    // Calculate statistics\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const totalTeams = standings.length;\n        const topTeam = standings.length > 0 ? standings[0] : null;\n        const totalGames = standings.reduce((sum, team)=>sum + team.playedGames, 0);\n        const totalGoals = standings.reduce((sum, team)=>sum + team.goalsFor, 0);\n        const avgGoalsPerGame = totalGames > 0 ? Math.round(totalGoals / totalGames * 100) / 100 : 0;\n        return {\n            totalTeams,\n            topTeam,\n            avgGoalsPerGame\n        };\n    }, [\n        standings\n    ]);\n    return {\n        standings,\n        loading,\n        error,\n        refreshStandings,\n        stats\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/hooks/leagues/useLeagueStandings.ts\n"));

/***/ })

});