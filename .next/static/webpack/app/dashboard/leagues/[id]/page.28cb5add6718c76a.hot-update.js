"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/leagues/[id]/page",{

/***/ "(app-pages-browser)/./src/lib/hooks/leagues/useLeagueStandings.ts":
/*!*****************************************************!*\
  !*** ./src/lib/hooks/leagues/useLeagueStandings.ts ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLeagueStandings: function() { return /* binding */ useLeagueStandings; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useLeagueStandings auto */ \n// Fetch standings from API\nconst fetchStandingsFromAPI = async (leagueId, season, format)=>{\n    try {\n        var _apiData_data, _apiData_data1;\n        const currentSeason = season || new Date().getFullYear();\n        const formatParam = format ? \"&format=\".concat(format) : \"\";\n        console.log(\"\\uD83D\\uDD04 Fetching standings for league:\", leagueId, \"season:\", currentSeason);\n        // Use proxy endpoint through Next.js frontend\n        const response = await fetch(\"/api/standings?league=\".concat(leagueId, \"&season=\").concat(currentSeason).concat(formatParam));\n        if (!response.ok) {\n            throw new Error(\"Failed to fetch standings: \".concat(response.status));\n        }\n        const apiData = await response.json();\n        console.log(\"✅ Standings API response:\", apiData);\n        console.log(\"\\uD83D\\uDD0D API data structure check:\", {\n            hasData: !!apiData.data,\n            isArray: Array.isArray(apiData.data),\n            dataLength: (_apiData_data = apiData.data) === null || _apiData_data === void 0 ? void 0 : _apiData_data.length,\n            firstItem: (_apiData_data1 = apiData.data) === null || _apiData_data1 === void 0 ? void 0 : _apiData_data1[0]\n        });\n        if (!apiData.data || !Array.isArray(apiData.data)) {\n            console.warn(\"⚠️ No standings data in API response, using fallback\");\n            return generateFallbackStandings(leagueId);\n        }\n        // Transform API data to our interface\n        return apiData.data.map((standing, index)=>{\n            var _standing_teamId, _standing_team_externalId, _standing_team, _standing_team1, _standing_team2, _standing_goals, _standing_goals1, _standing_team3, _standing_team4, _standing_team5;\n            // Parse form data from API (e.g., \"WWWWW\" -> ['W', 'W', 'W', 'W', 'W'])\n            let form = [];\n            if (standing.form && typeof standing.form === \"string\") {\n                form = standing.form.split(\"\").slice(0, 5);\n            } else {\n                // Fallback to random form if not provided\n                const formResults = [\n                    \"W\",\n                    \"L\",\n                    \"D\"\n                ];\n                form = Array.from({\n                    length: 5\n                }, ()=>formResults[Math.floor(Math.random() * formResults.length)]);\n            }\n            return {\n                position: standing.position || standing.rank || index + 1,\n                team: {\n                    id: ((_standing_teamId = standing.teamId) === null || _standing_teamId === void 0 ? void 0 : _standing_teamId.toString()) || ((_standing_team = standing.team) === null || _standing_team === void 0 ? void 0 : (_standing_team_externalId = _standing_team.externalId) === null || _standing_team_externalId === void 0 ? void 0 : _standing_team_externalId.toString()) || (index + 1).toString(),\n                    name: standing.teamName || ((_standing_team1 = standing.team) === null || _standing_team1 === void 0 ? void 0 : _standing_team1.name) || \"Team \".concat(index + 1),\n                    logo: standing.teamLogo || ((_standing_team2 = standing.team) === null || _standing_team2 === void 0 ? void 0 : _standing_team2.logo) || \"\"\n                },\n                points: standing.points || 0,\n                playedGames: standing.played || standing.playedGames || 0,\n                wins: standing.win || standing.wins || 0,\n                draws: standing.draw || standing.draws || 0,\n                losses: standing.lose || standing.losses || 0,\n                goalsFor: standing.goalsFor || ((_standing_goals = standing.goals) === null || _standing_goals === void 0 ? void 0 : _standing_goals.for) || 0,\n                goalsAgainst: standing.goalsAgainst || ((_standing_goals1 = standing.goals) === null || _standing_goals1 === void 0 ? void 0 : _standing_goals1.against) || 0,\n                goalDifference: standing.goalsDiff !== undefined ? standing.goalsDiff : standing.goalDifference !== undefined ? standing.goalDifference : (standing.goalsFor || 0) - (standing.goalsAgainst || 0),\n                form,\n                // Store original API fields\n                externalId: standing.externalId || standing.id,\n                teamId: standing.teamId || ((_standing_team3 = standing.team) === null || _standing_team3 === void 0 ? void 0 : _standing_team3.externalId),\n                teamName: standing.teamName || ((_standing_team4 = standing.team) === null || _standing_team4 === void 0 ? void 0 : _standing_team4.name),\n                teamLogo: standing.teamLogo || ((_standing_team5 = standing.team) === null || _standing_team5 === void 0 ? void 0 : _standing_team5.logo)\n            };\n        }).sort((a, b)=>a.position - b.position); // Ensure proper sorting by position\n    } catch (error) {\n        console.error(\"❌ Error fetching standings:\", error);\n        throw error;\n    }\n};\n// Generate fallback standings data for development/testing\nconst generateFallbackStandings = (leagueId)=>{\n    const teams = [\n        {\n            id: \"33\",\n            name: \"Manchester United\",\n            logo: \"https://media.api-sports.io/football/teams/33.png\"\n        },\n        {\n            id: \"50\",\n            name: \"Manchester City\",\n            logo: \"https://media.api-sports.io/football/teams/50.png\"\n        },\n        {\n            id: \"42\",\n            name: \"Arsenal\",\n            logo: \"https://media.api-sports.io/football/teams/42.png\"\n        },\n        {\n            id: \"40\",\n            name: \"Liverpool\",\n            logo: \"https://media.api-sports.io/football/teams/40.png\"\n        },\n        {\n            id: \"49\",\n            name: \"Chelsea\",\n            logo: \"https://media.api-sports.io/football/teams/49.png\"\n        },\n        {\n            id: \"47\",\n            name: \"Tottenham\",\n            logo: \"https://media.api-sports.io/football/teams/47.png\"\n        },\n        {\n            id: \"34\",\n            name: \"Newcastle\",\n            logo: \"https://media.api-sports.io/football/teams/34.png\"\n        },\n        {\n            id: \"66\",\n            name: \"Aston Villa\",\n            logo: \"https://media.api-sports.io/football/teams/66.png\"\n        },\n        {\n            id: \"51\",\n            name: \"Brighton\",\n            logo: \"https://media.api-sports.io/football/teams/51.png\"\n        },\n        {\n            id: \"39\",\n            name: \"Wolves\",\n            logo: \"https://media.api-sports.io/football/teams/39.png\"\n        }\n    ];\n    return teams.map((team, index)=>{\n        const played = 20 + Math.floor(Math.random() * 10);\n        const wins = Math.floor(Math.random() * played * 0.7);\n        const losses = Math.floor(Math.random() * (played - wins) * 0.6);\n        const draws = played - wins - losses;\n        const goalsFor = wins * 2 + draws + Math.floor(Math.random() * 10);\n        const goalsAgainst = losses * 2 + Math.floor(Math.random() * goalsFor * 0.8);\n        // Generate realistic form (last 5 matches)\n        const formResults = [\n            \"W\",\n            \"L\",\n            \"D\"\n        ];\n        const form = Array.from({\n            length: 5\n        }, ()=>formResults[Math.floor(Math.random() * formResults.length)]);\n        return {\n            position: index + 1,\n            team,\n            points: wins * 3 + draws,\n            playedGames: played,\n            wins,\n            draws,\n            losses,\n            goalsFor,\n            goalsAgainst,\n            goalDifference: goalsFor - goalsAgainst,\n            form,\n            externalId: parseInt(team.id),\n            teamId: parseInt(team.id),\n            teamName: team.name,\n            teamLogo: team.logo\n        };\n    }).sort((a, b)=>b.points - a.points || b.goalDifference - a.goalDifference);\n};\nconst useLeagueStandings = (param)=>{\n    let { leagueId, season, format = \"external\" } = param;\n    const [standings, setStandings] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const refreshStandings = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const standingsData = await fetchStandingsFromAPI(leagueId, season, format);\n            setStandings(standingsData);\n        } catch (err) {\n            console.error(\"Error fetching standings:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to fetch standings\");\n            // Use fallback data in case of error\n            setStandings(generateFallbackStandings(leagueId));\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (leagueId) {\n            refreshStandings();\n        }\n    }, [\n        leagueId,\n        season,\n        format\n    ]);\n    // Calculate statistics\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const totalTeams = standings.length;\n        const topTeam = standings.length > 0 ? standings[0] : null;\n        const totalGames = standings.reduce((sum, team)=>sum + team.playedGames, 0);\n        const totalGoals = standings.reduce((sum, team)=>sum + team.goalsFor, 0);\n        const avgGoalsPerGame = totalGames > 0 ? Math.round(totalGoals / totalGames * 100) / 100 : 0;\n        return {\n            totalTeams,\n            topTeam,\n            avgGoalsPerGame\n        };\n    }, [\n        standings\n    ]);\n    return {\n        standings,\n        loading,\n        error,\n        refreshStandings,\n        stats\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/hooks/leagues/useLeagueStandings.ts\n"));

/***/ })

});