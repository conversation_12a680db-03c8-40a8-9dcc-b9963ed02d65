"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/leagues/[id]/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/leagues/[id]/page.tsx":
/*!*************************************************!*\
  !*** ./src/app/dashboard/leagues/[id]/page.tsx ***!
  \*************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LeagueDetailPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/QueryClientProvider.mjs\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/lib/useMutation.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_modal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/modal */ \"(app-pages-browser)/./src/components/ui/modal.tsx\");\n/* harmony import */ var _components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/toggle-switch */ \"(app-pages-browser)/./src/components/ui/toggle-switch.tsx\");\n/* harmony import */ var _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/middleware/auth-guard */ \"(app-pages-browser)/./src/lib/middleware/auth-guard.tsx\");\n/* harmony import */ var _lib_hooks_useLeagues__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/hooks/useLeagues */ \"(app-pages-browser)/./src/lib/hooks/useLeagues.ts\");\n/* harmony import */ var _lib_api_leagues__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/api/leagues */ \"(app-pages-browser)/./src/lib/api/leagues.ts\");\n/* harmony import */ var _lib_utils_image__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/utils/image */ \"(app-pages-browser)/./src/lib/utils/image.ts\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Clock_Edit_Globe_RefreshCw_Trash2_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Clock,Edit,Globe,RefreshCw,Trash2,Trophy,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Clock_Edit_Globe_RefreshCw_Trash2_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Clock,Edit,Globe,RefreshCw,Trash2,Trophy,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Clock_Edit_Globe_RefreshCw_Trash2_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Clock,Edit,Globe,RefreshCw,Trash2,Trophy,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Clock_Edit_Globe_RefreshCw_Trash2_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Clock,Edit,Globe,RefreshCw,Trash2,Trophy,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Clock_Edit_Globe_RefreshCw_Trash2_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Clock,Edit,Globe,RefreshCw,Trash2,Trophy,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Clock_Edit_Globe_RefreshCw_Trash2_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Clock,Edit,Globe,RefreshCw,Trash2,Trophy,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Clock_Edit_Globe_RefreshCw_Trash2_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Clock,Edit,Globe,RefreshCw,Trash2,Trophy,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Clock_Edit_Globe_RefreshCw_Trash2_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Clock,Edit,Globe,RefreshCw,Trash2,Trophy,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Clock_Edit_Globe_RefreshCw_Trash2_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Clock,Edit,Globe,RefreshCw,Trash2,Trophy,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Clock_Edit_Globe_RefreshCw_Trash2_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowLeft,Calendar,Clock,Edit,Globe,RefreshCw,Trash2,Trophy,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _components_leagues_detail_LeagueStatistics__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/leagues/detail/LeagueStatistics */ \"(app-pages-browser)/./src/components/leagues/detail/LeagueStatistics.tsx\");\n/* harmony import */ var _components_leagues_detail_LeagueTeamsSection__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/leagues/detail/LeagueTeamsSection */ \"(app-pages-browser)/./src/components/leagues/detail/LeagueTeamsSection.tsx\");\n/* harmony import */ var _components_leagues_detail_LeagueFixturesPreview__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/leagues/detail/LeagueFixturesPreview */ \"(app-pages-browser)/./src/components/leagues/detail/LeagueFixturesPreview.tsx\");\n/* harmony import */ var _components_leagues_detail_LeagueStandings__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/leagues/detail/LeagueStandings */ \"(app-pages-browser)/./src/components/leagues/detail/LeagueStandings.tsx\");\n/* harmony import */ var _components_leagues_detail_LeagueActionButtons__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/leagues/detail/LeagueActionButtons */ \"(app-pages-browser)/./src/components/leagues/detail/LeagueActionButtons.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Import new modular components\n\n\n\n\n\nfunction LeagueDetailPage() {\n    var _league_season_detail_coverage_fixtures;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { isEditor, isAdmin } = (0,_lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__.usePermissions)();\n    const [deleteModalOpen, setDeleteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const queryClient = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_19__.useQueryClient)();\n    // Parse externalId and season from URL parameter\n    const idParam = params.id;\n    const [externalIdStr, seasonStr] = idParam.includes(\"-\") ? idParam.split(\"-\") : [\n        idParam,\n        undefined\n    ];\n    const leagueId = parseInt(externalIdStr);\n    const seasonParam = seasonStr ? parseInt(seasonStr) : undefined;\n    // Fetch league details\n    const { league, isLoading, error } = (0,_lib_hooks_useLeagues__WEBPACK_IMPORTED_MODULE_10__.useLeague)(leagueId, seasonParam);\n    // Delete mutation\n    const deleteMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation)({\n        mutationFn: ()=>_lib_api_leagues__WEBPACK_IMPORTED_MODULE_11__.leaguesApi.deleteLeague(leagueId, seasonParam),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"leagues\"\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_13__.toast.success(\"League deleted successfully\");\n            setDeleteModalOpen(false);\n            router.push(\"/dashboard/leagues\");\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_13__.toast.error(error.message || \"Failed to delete league\");\n            setDeleteModalOpen(false);\n        }\n    });\n    // Toggle Status mutation\n    const toggleStatusMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation)({\n        mutationFn: (active)=>_lib_api_leagues__WEBPACK_IMPORTED_MODULE_11__.leaguesApi.updateLeague(leagueId, {\n                active\n            }, seasonParam),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"leagues\",\n                    leagueId,\n                    seasonParam\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"leagues\"\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_13__.toast.success(\"League status updated successfully\");\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_13__.toast.error(error.message || \"Failed to update league status\");\n        }\n    });\n    // Toggle Hot League mutation\n    const toggleHotMutation = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation)({\n        mutationFn: (isHot)=>_lib_api_leagues__WEBPACK_IMPORTED_MODULE_11__.leaguesApi.updateLeague(leagueId, {\n                isHot\n            }, seasonParam),\n        onSuccess: ()=>{\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"leagues\",\n                    leagueId,\n                    seasonParam\n                ]\n            });\n            queryClient.invalidateQueries({\n                queryKey: [\n                    \"leagues\"\n                ]\n            });\n            sonner__WEBPACK_IMPORTED_MODULE_13__.toast.success(\"League hot status updated successfully\");\n        },\n        onError: (error)=>{\n            sonner__WEBPACK_IMPORTED_MODULE_13__.toast.error(error.message || \"Failed to update league hot status\");\n        }\n    });\n    // Handlers\n    const handleEdit = ()=>{\n        router.push(\"/dashboard/leagues/\".concat(idParam, \"/edit\"));\n    };\n    const handleDelete = ()=>{\n        setDeleteModalOpen(true);\n    };\n    const confirmDelete = ()=>{\n        deleteMutation.mutate();\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                            className: \"h-10 w-20\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 31\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                            className: \"h-8 w-48\"\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 31\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 25\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-2 space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                className: \"h-96\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 37\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 31\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-64\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_6__.Skeleton, {\n                                    className: \"h-48\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                            lineNumber: 124,\n                            columnNumber: 31\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 25\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n            lineNumber: 114,\n            columnNumber: 19\n        }, this);\n    }\n    if (error || !league) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"outline\",\n                        size: \"sm\",\n                        onClick: ()=>router.back(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Clock_Edit_Globe_RefreshCw_Trash2_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                className: \"w-4 h-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 37\n                            }, this),\n                            \"Back\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 31\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 25\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"flex items-center justify-center h-96\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Clock_Edit_Globe_RefreshCw_Trash2_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                    className: \"w-12 h-12 text-gray-400 mx-auto mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 43\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900 mb-2\",\n                                    children: \"League not found\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 43\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: \"The league you're looking for doesn't exist or you don't have permission to view it.\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 43\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 37\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 31\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                    lineNumber: 147,\n                    columnNumber: 25\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n            lineNumber: 135,\n            columnNumber: 19\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>router.back(),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Clock_Edit_Globe_RefreshCw_Trash2_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 37\n                                    }, this),\n                                    \"Back\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 31\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_12__.buildLeagueLogoUrl)(league.logo) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_12__.buildLeagueLogoUrl)(league.logo) || \"\",\n                                        alt: league.name,\n                                        className: \"w-12 h-12 object-contain\",\n                                        onError: (e)=>{\n                                            const target = e.target;\n                                            target.style.display = \"none\";\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 43\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Clock_Edit_Globe_RefreshCw_Trash2_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"w-6 h-6 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 49\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 43\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-2xl font-bold tracking-tight\",\n                                                children: league.name\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 43\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Clock_Edit_Globe_RefreshCw_Trash2_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: league.country\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    league.type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"•\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 61\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"capitalize\",\n                                                                children: league.type\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 61\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 43\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 37\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 31\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 25\n                    }, this),\n                    (isEditor() || isAdmin()) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_leagues_detail_LeagueActionButtons__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    leagueId: leagueId.toString()\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 43\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 37\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    isEditor() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: handleEdit,\n                                        variant: \"outline\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Clock_Edit_Globe_RefreshCw_Trash2_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 55\n                                            }, this),\n                                            \"Edit League\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 49\n                                    }, this),\n                                    isAdmin() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"destructive\",\n                                        onClick: handleDelete,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Clock_Edit_Globe_RefreshCw_Trash2_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 55\n                                            }, this),\n                                            \"Delete League\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 49\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 37\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 31\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                lineNumber: 167,\n                columnNumber: 19\n            }, this),\n            isEditor() || isAdmin() ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_8__.ToggleSwitch, {\n                        checked: league.active,\n                        onCheckedChange: (checked)=>toggleStatusMutation.mutate(checked),\n                        label: \"Active Status\",\n                        description: \"Enable or disable this league\",\n                        disabled: toggleStatusMutation.isLoading,\n                        variant: \"success\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 31\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toggle_switch__WEBPACK_IMPORTED_MODULE_8__.ToggleSwitch, {\n                        checked: league.isHot || false,\n                        onCheckedChange: (checked)=>toggleHotMutation.mutate(checked),\n                        label: \"Hot League\",\n                        description: \"Mark as featured/popular league\",\n                        disabled: toggleHotMutation.isLoading,\n                        variant: \"danger\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 31\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                lineNumber: 240,\n                columnNumber: 25\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                        variant: league.active ? \"default\" : \"secondary\",\n                        children: league.active ? \"Active\" : \"Inactive\"\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 31\n                    }, this),\n                    league.isHot && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                        variant: \"destructive\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Clock_Edit_Globe_RefreshCw_Trash2_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                className: \"w-3 h-3 mr-1\"\n                            }, void 0, false, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 43\n                            }, this),\n                            \"Hot League\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                        lineNumber: 264,\n                        columnNumber: 37\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                lineNumber: 259,\n                columnNumber: 25\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Clock_Edit_Globe_RefreshCw_Trash2_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                    lineNumber: 279,\n                                                    columnNumber: 49\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"League Information\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 49\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 43\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 37\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-500\",\n                                                            children: \"League Name\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-medium\",\n                                                            children: league.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 55\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                    lineNumber: 285,\n                                                    columnNumber: 49\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-500\",\n                                                            children: \"Country\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_12__.buildCountryFlagUrl)(league.countryFlag) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                    src: (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_12__.buildCountryFlagUrl)(league.countryFlag) || \"\",\n                                                                    alt: league.country,\n                                                                    className: \"w-5 h-5\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                                    lineNumber: 294,\n                                                                    columnNumber: 67\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-lg font-medium\",\n                                                                    children: league.country\n                                                                }, void 0, false, {\n                                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                                    lineNumber: 300,\n                                                                    columnNumber: 61\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 55\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 49\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-500\",\n                                                            children: \"League Type\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-medium capitalize\",\n                                                            children: league.type || \"N/A\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                            lineNumber: 306,\n                                                            columnNumber: 55\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 49\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"text-sm font-medium text-gray-500\",\n                                                            children: \"External ID\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                            lineNumber: 312,\n                                                            columnNumber: 55\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-lg font-medium\",\n                                                            children: league.externalId\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 55\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 49\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                            lineNumber: 284,\n                                            columnNumber: 43\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 37\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 31\n                            }, this),\n                            league.season_detail && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Clock_Edit_Globe_RefreshCw_Trash2_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 55\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Season Details\"\n                                                }, void 0, false, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 55\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 49\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 43\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium text-gray-500\",\n                                                                children: \"Season Year\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                                lineNumber: 331,\n                                                                columnNumber: 61\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg font-medium\",\n                                                                children: league.season_detail.year\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                                lineNumber: 332,\n                                                                columnNumber: 61\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 55\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium text-gray-500\",\n                                                                children: \"Start Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 61\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg font-medium\",\n                                                                children: league.season_detail.start\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                                lineNumber: 337,\n                                                                columnNumber: 61\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 55\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"text-sm font-medium text-gray-500\",\n                                                                children: \"End Date\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                                lineNumber: 341,\n                                                                columnNumber: 61\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg font-medium\",\n                                                                children: league.season_detail.end\n                                                            }, void 0, false, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                                lineNumber: 342,\n                                                                columnNumber: 61\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 55\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 49\n                                            }, this),\n                                            league.season_detail.current && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                    variant: \"secondary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Clock_Edit_Globe_RefreshCw_Trash2_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                            className: \"w-3 h-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 67\n                                                        }, this),\n                                                        \"Current Season\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                    lineNumber: 348,\n                                                    columnNumber: 61\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 55\n                                            }, this),\n                                            league.season_detail.coverage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-500 mb-3\",\n                                                        children: \"Coverage Details\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                        lineNumber: 358,\n                                                        columnNumber: 61\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 rounded-full \".concat(((_league_season_detail_coverage_fixtures = league.season_detail.coverage.fixtures) === null || _league_season_detail_coverage_fixtures === void 0 ? void 0 : _league_season_detail_coverage_fixtures.events) ? \"bg-green-500\" : \"bg-red-500\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                                        lineNumber: 361,\n                                                                        columnNumber: 73\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Fixtures\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                                        lineNumber: 362,\n                                                                        columnNumber: 73\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 67\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 rounded-full \".concat(league.season_detail.coverage.standings ? \"bg-green-500\" : \"bg-red-500\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                                        lineNumber: 365,\n                                                                        columnNumber: 73\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Standings\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                                        lineNumber: 366,\n                                                                        columnNumber: 73\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 67\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 rounded-full \".concat(league.season_detail.coverage.players ? \"bg-green-500\" : \"bg-red-500\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                                        lineNumber: 369,\n                                                                        columnNumber: 73\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Players\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                                        lineNumber: 370,\n                                                                        columnNumber: 73\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 67\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 rounded-full \".concat(league.season_detail.coverage.top_scorers ? \"bg-green-500\" : \"bg-red-500\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                                        lineNumber: 373,\n                                                                        columnNumber: 73\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: \"Top Scorers\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                                        lineNumber: 374,\n                                                                        columnNumber: 73\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 67\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 61\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 55\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 43\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                lineNumber: 321,\n                                columnNumber: 37\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 25\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: \"Quick Stats\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 43\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 37\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: league.active ? \"default\" : \"secondary\",\n                                                        children: league.active ? \"Active\" : \"Inactive\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 43\n                                            }, this),\n                                            league.isHot && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Popularity\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 55\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                        variant: \"destructive\",\n                                                        children: \"Hot\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 55\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 49\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"League ID\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 49\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: [\n                                                            \"#\",\n                                                            league.externalId\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 49\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 43\n                                            }, this),\n                                            league.season_detail && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"Current Season\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 55\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: league.season_detail.year\n                                                    }, void 0, false, {\n                                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                        lineNumber: 414,\n                                                        columnNumber: 55\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 49\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 37\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 31\n                            }, this),\n                            (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_12__.buildLeagueLogoUrl)(league.logo) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: \"League Logo\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 49\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 43\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: (0,_lib_utils_image__WEBPACK_IMPORTED_MODULE_12__.buildLeagueLogoUrl)(league.logo) || \"\",\n                                                alt: league.name,\n                                                className: \"w-32 h-32 object-contain\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 55\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 49\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 43\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 37\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                        lineNumber: 385,\n                        columnNumber: 25\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                lineNumber: 272,\n                columnNumber: 19\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8 mt-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_leagues_detail_LeagueStatistics__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        league: league,\n                        season: seasonParam\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                        lineNumber: 443,\n                        columnNumber: 25\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_leagues_detail_LeagueStandings__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                        leagueId: leagueId.toString(),\n                        season: seasonParam\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                        lineNumber: 446,\n                        columnNumber: 25\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_leagues_detail_LeagueTeamsSection__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        leagueId: leagueId.toString(),\n                        season: seasonParam\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                        lineNumber: 449,\n                        columnNumber: 25\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_leagues_detail_LeagueFixturesPreview__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                        leagueId: leagueId.toString(),\n                        season: seasonParam\n                    }, void 0, false, {\n                        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                        lineNumber: 452,\n                        columnNumber: 25\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                lineNumber: 441,\n                columnNumber: 19\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_modal__WEBPACK_IMPORTED_MODULE_7__.Modal, {\n                isOpen: deleteModalOpen,\n                onClose: ()=>setDeleteModalOpen(false),\n                title: \"Delete League\",\n                description: \"Are you sure you want to delete this league? This action cannot be undone.\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3 p-4 bg-red-50 rounded-lg border border-red-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Clock_Edit_Globe_RefreshCw_Trash2_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                    className: \"h-5 w-5 text-red-600 flex-shrink-0\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-red-800\",\n                                            children: \"This will permanently delete the league:\"\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 43\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-700 mt-1\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: league.name\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                lineNumber: 470,\n                                                columnNumber: 49\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 43\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-red-600 mt-1\",\n                                            children: [\n                                                league.country,\n                                                \" • \",\n                                                league.type\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 43\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 31\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-end space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setDeleteModalOpen(false),\n                                    disabled: deleteMutation.isLoading,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 37\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"destructive\",\n                                    onClick: confirmDelete,\n                                    disabled: deleteMutation.isLoading,\n                                    children: deleteMutation.isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Clock_Edit_Globe_RefreshCw_Trash2_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_30__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 55\n                                            }, this),\n                                            \"Deleting...\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowLeft_Calendar_Clock_Edit_Globe_RefreshCw_Trash2_Trophy_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                                lineNumber: 498,\n                                                columnNumber: 55\n                                            }, this),\n                                            \"Delete League\"\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 37\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                            lineNumber: 478,\n                            columnNumber: 31\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                    lineNumber: 462,\n                    columnNumber: 25\n                }, this)\n            }, void 0, false, {\n                fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n                lineNumber: 456,\n                columnNumber: 19\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/home/<USER>/FECMS-sport/src/app/dashboard/leagues/[id]/page.tsx\",\n        lineNumber: 165,\n        columnNumber: 13\n    }, this);\n}\n_s(LeagueDetailPage, \"NEFOm6iA1nR6REBsKdPyDbqkW0o=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter,\n        _lib_middleware_auth_guard__WEBPACK_IMPORTED_MODULE_9__.usePermissions,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_19__.useQueryClient,\n        _lib_hooks_useLeagues__WEBPACK_IMPORTED_MODULE_10__.useLeague,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation,\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_20__.useMutation\n    ];\n});\n_c = LeagueDetailPage;\nvar _c;\n$RefreshReg$(_c, \"LeagueDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/leagues/[id]/page.tsx\n"));

/***/ })

});