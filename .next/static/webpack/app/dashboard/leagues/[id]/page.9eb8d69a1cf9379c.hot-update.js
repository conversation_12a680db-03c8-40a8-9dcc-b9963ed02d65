"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/leagues/[id]/page",{

/***/ "(app-pages-browser)/./src/lib/hooks/leagues/useLeagueStandings.ts":
/*!*****************************************************!*\
  !*** ./src/lib/hooks/leagues/useLeagueStandings.ts ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLeagueStandings: function() { return /* binding */ useLeagueStandings; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useLeagueStandings auto */ \n// Fetch standings from API\nconst fetchStandingsFromAPI = async (leagueId, season, format)=>{\n    try {\n        var _apiData_data, _apiData_data1;\n        const currentSeason = season || new Date().getFullYear();\n        const formatParam = format ? \"&format=\".concat(format) : \"\";\n        console.log(\"\\uD83D\\uDD04 Fetching standings for league:\", leagueId, \"season:\", currentSeason);\n        // Use proxy endpoint through Next.js frontend with cache busting\n        const timestamp = Date.now();\n        const apiUrl = \"/api/standings?league=\".concat(leagueId, \"&season=\").concat(currentSeason).concat(formatParam, \"&_t=\").concat(timestamp);\n        console.log(\"\\uD83C\\uDF10 Making API call to:\", apiUrl);\n        const response = await fetch(apiUrl, {\n            method: \"GET\",\n            headers: {\n                \"Cache-Control\": \"no-cache, no-store, must-revalidate\",\n                \"Pragma\": \"no-cache\",\n                \"Expires\": \"0\"\n            }\n        });\n        if (!response.ok) {\n            console.error(\"❌ API Response not OK:\", response.status, response.statusText);\n            throw new Error(\"Failed to fetch standings: \".concat(response.status, \" \").concat(response.statusText));\n        }\n        const apiData = await response.json();\n        console.log(\"✅ Standings API response:\", apiData);\n        console.log(\"\\uD83D\\uDD0D API data structure check:\", {\n            hasData: !!apiData.data,\n            isArray: Array.isArray(apiData.data),\n            dataLength: (_apiData_data = apiData.data) === null || _apiData_data === void 0 ? void 0 : _apiData_data.length,\n            firstItem: (_apiData_data1 = apiData.data) === null || _apiData_data1 === void 0 ? void 0 : _apiData_data1[0]\n        });\n        if (!apiData.data || !Array.isArray(apiData.data)) {\n            console.error(\"❌ Invalid API response structure:\", {\n                hasData: !!apiData.data,\n                dataType: typeof apiData.data,\n                isArray: Array.isArray(apiData.data),\n                fullResponse: apiData\n            });\n            console.warn(\"⚠️ No standings data in API response, using fallback\");\n            return generateFallbackStandings(leagueId);\n        }\n        // Transform API data to our interface\n        console.log(\"\\uD83D\\uDD04 Transforming API data to interface, count:\", apiData.data.length);\n        const transformedData = apiData.data.map((standing, index)=>{\n            var _standing_teamId, _standing_team_externalId, _standing_team, _standing_team1, _standing_team2, _standing_goals, _standing_goals1, _standing_team3, _standing_team4, _standing_team5;\n            // Parse form data from API (e.g., \"WWWWW\" -> ['W', 'W', 'W', 'W', 'W'])\n            let form = [];\n            if (standing.form && typeof standing.form === \"string\") {\n                form = standing.form.split(\"\").slice(0, 5);\n            } else {\n                // Fallback to random form if not provided\n                const formResults = [\n                    \"W\",\n                    \"L\",\n                    \"D\"\n                ];\n                form = Array.from({\n                    length: 5\n                }, ()=>formResults[Math.floor(Math.random() * formResults.length)]);\n            }\n            return {\n                position: standing.position || standing.rank || index + 1,\n                team: {\n                    id: ((_standing_teamId = standing.teamId) === null || _standing_teamId === void 0 ? void 0 : _standing_teamId.toString()) || ((_standing_team = standing.team) === null || _standing_team === void 0 ? void 0 : (_standing_team_externalId = _standing_team.externalId) === null || _standing_team_externalId === void 0 ? void 0 : _standing_team_externalId.toString()) || (index + 1).toString(),\n                    name: standing.teamName || ((_standing_team1 = standing.team) === null || _standing_team1 === void 0 ? void 0 : _standing_team1.name) || \"Team \".concat(index + 1),\n                    logo: standing.teamLogo || ((_standing_team2 = standing.team) === null || _standing_team2 === void 0 ? void 0 : _standing_team2.logo) || \"\"\n                },\n                points: standing.points || 0,\n                playedGames: standing.played || standing.playedGames || 0,\n                wins: standing.win || standing.wins || 0,\n                draws: standing.draw || standing.draws || 0,\n                losses: standing.lose || standing.losses || 0,\n                goalsFor: standing.goalsFor || ((_standing_goals = standing.goals) === null || _standing_goals === void 0 ? void 0 : _standing_goals.for) || 0,\n                goalsAgainst: standing.goalsAgainst || ((_standing_goals1 = standing.goals) === null || _standing_goals1 === void 0 ? void 0 : _standing_goals1.against) || 0,\n                goalDifference: standing.goalsDiff !== undefined ? standing.goalsDiff : standing.goalDifference !== undefined ? standing.goalDifference : (standing.goalsFor || 0) - (standing.goalsAgainst || 0),\n                form,\n                // Store original API fields\n                externalId: standing.externalId || standing.id,\n                teamId: standing.teamId || ((_standing_team3 = standing.team) === null || _standing_team3 === void 0 ? void 0 : _standing_team3.externalId),\n                teamName: standing.teamName || ((_standing_team4 = standing.team) === null || _standing_team4 === void 0 ? void 0 : _standing_team4.name),\n                teamLogo: standing.teamLogo || ((_standing_team5 = standing.team) === null || _standing_team5 === void 0 ? void 0 : _standing_team5.logo)\n            };\n        });\n        const sortedData = transformedData.sort((a, b)=>a.position - b.position);\n        console.log(\"✅ Transformed standings data:\", sortedData.slice(0, 3).map((s)=>({\n                position: s.position,\n                teamName: s.team.name,\n                points: s.points\n            })));\n        return sortedData;\n    } catch (error) {\n        console.error(\"❌ Error fetching standings:\", error);\n        throw error;\n    }\n};\n// Generate fallback standings data for development/testing\nconst generateFallbackStandings = (leagueId)=>{\n    const teams = [\n        {\n            id: \"33\",\n            name: \"Manchester United\",\n            logo: \"https://media.api-sports.io/football/teams/33.png\"\n        },\n        {\n            id: \"50\",\n            name: \"Manchester City\",\n            logo: \"https://media.api-sports.io/football/teams/50.png\"\n        },\n        {\n            id: \"42\",\n            name: \"Arsenal\",\n            logo: \"https://media.api-sports.io/football/teams/42.png\"\n        },\n        {\n            id: \"40\",\n            name: \"Liverpool\",\n            logo: \"https://media.api-sports.io/football/teams/40.png\"\n        },\n        {\n            id: \"49\",\n            name: \"Chelsea\",\n            logo: \"https://media.api-sports.io/football/teams/49.png\"\n        },\n        {\n            id: \"47\",\n            name: \"Tottenham\",\n            logo: \"https://media.api-sports.io/football/teams/47.png\"\n        },\n        {\n            id: \"34\",\n            name: \"Newcastle\",\n            logo: \"https://media.api-sports.io/football/teams/34.png\"\n        },\n        {\n            id: \"66\",\n            name: \"Aston Villa\",\n            logo: \"https://media.api-sports.io/football/teams/66.png\"\n        },\n        {\n            id: \"51\",\n            name: \"Brighton\",\n            logo: \"https://media.api-sports.io/football/teams/51.png\"\n        },\n        {\n            id: \"39\",\n            name: \"Wolves\",\n            logo: \"https://media.api-sports.io/football/teams/39.png\"\n        }\n    ];\n    return teams.map((team, index)=>{\n        const played = 20 + Math.floor(Math.random() * 10);\n        const wins = Math.floor(Math.random() * played * 0.7);\n        const losses = Math.floor(Math.random() * (played - wins) * 0.6);\n        const draws = played - wins - losses;\n        const goalsFor = wins * 2 + draws + Math.floor(Math.random() * 10);\n        const goalsAgainst = losses * 2 + Math.floor(Math.random() * goalsFor * 0.8);\n        // Generate realistic form (last 5 matches)\n        const formResults = [\n            \"W\",\n            \"L\",\n            \"D\"\n        ];\n        const form = Array.from({\n            length: 5\n        }, ()=>formResults[Math.floor(Math.random() * formResults.length)]);\n        return {\n            position: index + 1,\n            team,\n            points: wins * 3 + draws,\n            playedGames: played,\n            wins,\n            draws,\n            losses,\n            goalsFor,\n            goalsAgainst,\n            goalDifference: goalsFor - goalsAgainst,\n            form,\n            externalId: parseInt(team.id),\n            teamId: parseInt(team.id),\n            teamName: team.name,\n            teamLogo: team.logo\n        };\n    }).sort((a, b)=>b.points - a.points || b.goalDifference - a.goalDifference);\n};\nconst useLeagueStandings = (param)=>{\n    let { leagueId, season, format = \"external\" } = param;\n    const [standings, setStandings] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    console.log(\"\\uD83C\\uDFD7️ useLeagueStandings hook initialized:\", {\n        leagueId,\n        season,\n        format\n    });\n    const refreshStandings = async ()=>{\n        try {\n            var _standingsData__team, _standingsData_;\n            setLoading(true);\n            setError(null);\n            const standingsData = await fetchStandingsFromAPI(leagueId, season, format);\n            console.log(\"\\uD83C\\uDFAF Setting standings data in hook:\", standingsData.length, \"teams\");\n            console.log(\"\\uD83C\\uDFAF First team in standings:\", (_standingsData_ = standingsData[0]) === null || _standingsData_ === void 0 ? void 0 : (_standingsData__team = _standingsData_.team) === null || _standingsData__team === void 0 ? void 0 : _standingsData__team.name);\n            setStandings(standingsData);\n        } catch (err) {\n            console.error(\"❌ Error fetching standings:\", err);\n            setError(err instanceof Error ? err.message : \"Failed to fetch standings\");\n            // In development, don't use fallback to debug the real issue\n            if (true) {\n                console.error(\"\\uD83D\\uDEA8 DEVELOPMENT MODE: Not using fallback data to debug API issue\");\n                setStandings([]); // Show empty instead of fallback\n            } else {}\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (leagueId) {\n            console.log(\"\\uD83D\\uDD04 useLeagueStandings useEffect triggered:\", {\n                leagueId,\n                season,\n                format\n            });\n            // Clear any existing data first\n            setStandings([]);\n            setError(null);\n            refreshStandings();\n        }\n    }, [\n        leagueId,\n        season,\n        format\n    ]);\n    // Calculate statistics\n    const stats = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>{\n        const totalTeams = standings.length;\n        const topTeam = standings.length > 0 ? standings[0] : null;\n        const totalGames = standings.reduce((sum, team)=>sum + team.playedGames, 0);\n        const totalGoals = standings.reduce((sum, team)=>sum + team.goalsFor, 0);\n        const avgGoalsPerGame = totalGames > 0 ? Math.round(totalGoals / totalGames * 100) / 100 : 0;\n        return {\n            totalTeams,\n            topTeam,\n            avgGoalsPerGame\n        };\n    }, [\n        standings\n    ]);\n    return {\n        standings,\n        loading,\n        error,\n        refreshStandings,\n        stats\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/hooks/leagues/useLeagueStandings.ts\n"));

/***/ })

});